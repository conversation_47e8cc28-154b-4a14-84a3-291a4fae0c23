# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import copy

import netaddr
from neutron_lib import constants as lib_constants
from neutron_lib.db import utils as db_utils
from neutron_lib.exceptions import metering as metering_exc
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_db import exception as db_exc
from oslo_log import helpers as log_helpers
from oslo_log import log as logging
from oslo_utils import timeutils
from oslo_utils import uuidutils
from sqlalchemy import and_
from sqlalchemy.orm import exc

from neutron.api.rpc.agentnotifiers import metering_rpc_agent_api
from neutron.common import constants
from neutron.db import _model_query as model_query
from neutron.db import api as db_api
from neutron.db import common_db_mixin as base_db
from neutron.db import l3_dvr_db
from neutron.db.models import address_group as ag_model
from neutron.db.models import agent as agent_model
from neutron.db.models import l3 as l3_models
from neutron.db.models import l3_attrs
from neutron.db.models import l3agent as routerl3binding
from neutron.db.models import l3ha as l3ha
from neutron.db.models import metering as metering_db
from neutron.db import models_v2
from neutron.db.network_dhcp_agent_binding import models as ndab_models
from neutron.extensions import address_group as ext_ag
from neutron.extensions import metering

from neutron.objects import base as base_obj
from neutron.objects import metering as metering_objs
from neutron.objects import port_forwarding as pf
from neutron.objects import router as l3_obj

LOG = logging.getLogger(__name__)


class MeteringDbMixin(metering.MeteringPluginBase,
                      base_db.CommonDbMixin):

    def __init__(self):
        self.meter_rpc = metering_rpc_agent_api.MeteringAgentNotifyAPI()
        self._core_plugin = directory.get_plugin()
        self.l3plugin = directory.get_plugin(plugin_constants.L3)

    @staticmethod
    def _make_metering_label_dict(metering_label, fields=None):
        res = {'id': metering_label['id'],
               'name': metering_label['name'],
               'description': metering_label['description'],
               'shared': metering_label['shared'],
               'tenant_id': metering_label['tenant_id'],
               'floatingip_id': metering_label['floatingip_id'],
               'triggeredtype': metering_label['triggeredtype'],
               'ipversion': metering_label['ipversion'],
               'router_id': metering_label['router_id'],
               'type': metering_label['type']}
        return db_utils.resource_fields(res, fields)

    def create_metering_label(self, context, metering_label):
        with db_api.context_manager.writer.using(context):
            metering_obj = metering_objs.MeteringLabel(
                context, id=uuidutils.generate_uuid(),
                description=metering_label['description'],
                project_id=metering_label['tenant_id'],
                name=metering_label['name'],
                shared=metering_label['shared'],
                floatingip_id=metering_label['floatingip_id'],
                triggeredtype=metering_label['triggeredtype'],
                ipversion=metering_label['ipversion'],
                router_id=metering_label['router_id'],
                type=metering_label.get('type', constants.METERING_TYPE_EIP))
            metering_obj.create()
        return self._make_metering_label_dict(metering_obj)

    def _get_metering_label(self, context, label_id):
        metering_label = metering_objs.MeteringLabel.get_object(context,
                                                                id=label_id)
        if not metering_label:
            raise metering_exc.MeteringLabelNotFound(label_id=label_id)
        return metering_label

    def delete_metering_label(self, context, label_id):
        deleted = metering_objs.MeteringLabel.delete_objects(
            context, id=label_id)
        if not deleted:
            raise metering_exc.MeteringLabelNotFound(label_id=label_id)

    def get_metering_label(self, context, label_id, fields=None):
        return self._make_metering_label_dict(
            self._get_metering_label(context, label_id), fields)

    def get_metering_labels(self, context, filters=None, fields=None,
                            sorts=None, limit=None, marker=None,
                            page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        metering_labels = metering_objs.MeteringLabel.get_objects(
            context, _pager=pager, **filters)
        return [self._make_metering_label_dict(ml) for ml in metering_labels]

    @staticmethod
    def _make_metering_label_rule_dict(metering_label_rule, fields=None):
        res = {'id': metering_label_rule['id'],
               'metering_label_id': metering_label_rule['metering_label_id'],
               'direction': metering_label_rule['direction'],
               'remote_ip_prefix': metering_label_rule['remote_ip_prefix'],
               'excluded': metering_label_rule['excluded'],
               'address_group_id': metering_label_rule.get('address_group_id'),
               'type': metering_label_rule.get('type')}
        return db_utils.resource_fields(res, fields)

    def get_metering_label_rules(self, context, filters=None, fields=None,
                                 sorts=None, limit=None, marker=None,
                                 page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        metering_label_rules = metering_objs.MeteringLabelRule.get_objects(
            context, _pager=pager, **filters)
        return [self._make_metering_label_rule_dict(mlr)
                for mlr in metering_label_rules]

    def _get_metering_label_rule(self, context, rule_id):
        metering_label_rule = metering_objs.MeteringLabelRule.get_object(
            context, id=rule_id)
        if not metering_label_rule:
            raise metering_exc.MeteringLabelRuleNotFound(rule_id=rule_id)
        return metering_label_rule

    def get_metering_label_rule(self, context, rule_id, fields=None):
        return self._make_metering_label_rule_dict(
            self._get_metering_label_rule(context, rule_id), fields)

    def _validate_address_group(self, context, address_group_id):
        if address_group_id:
            try:
                model_query.get_by_id(
                    context, ag_model.AddressGroup,
                    address_group_id)
            except exc.NoResultFound:
                raise ext_ag.AddressGroupNotFound(id=address_group_id)

    def create_metering_label_rule(self, context, metering_label_rule):
        m = metering_label_rule['metering_label_rule']
        try:
            with db_api.context_manager.writer.using(context):
                label_id = m['metering_label_id']
                ip_prefix = m['remote_ip_prefix']
                direction = m['direction']
                excluded = m['excluded']
                address_group_id = m.get('address_group_id')
                type = m.get('type', constants.METERING_TYPE_EIP)

                self._validate_address_group(context, address_group_id)
                rule = metering_objs.MeteringLabelRule(
                    context, id=uuidutils.generate_uuid(),
                    metering_label_id=label_id, direction=direction,
                    excluded=excluded,
                    remote_ip_prefix=str(ip_prefix),
                    address_group_id=address_group_id,
                    type=type)
                rule.create()
        except db_exc.DBReferenceError:
            raise metering_exc.MeteringLabelNotFound(label_id=label_id)

        return self._make_metering_label_rule_dict(rule)

    def delete_metering_label_rule(self, context, rule_id):
        with db_api.context_manager.writer.using(context):
            rule = self._get_metering_label_rule(context, rule_id)
            rule.delete()

        return self._make_metering_label_rule_dict(rule)

    def _get_metering_rules_dict(self, metering_label):
        rules = []
        for rule in metering_label.rules:
            rule_dict = self._make_metering_label_rule_dict(rule)
            rules.append(rule_dict)

        return rules

    def _make_router_dict(self, router):
        distributed = l3_dvr_db.is_distributed_router(router)
        res = {'id': router['id'],
               # 'name': router['name'],
               'tenant_id': router['tenant_id'],
               # 'admin_state_up': router['admin_state_up'],
               # 'status': router['status'],
               'gw_port_id': router['gw_port_id'],
               'distributed': distributed,
               constants.METERING_LABEL_KEY: []}

        return res

    @staticmethod
    def _each_floatingip_having_fixed_ip(floatingips):
        for floatingip in floatingips or []:
            fixed_ip = floatingip.get('fixed_ip_address', None)
            fixed_port_id = floatingip.get('port_id', None)
            if not fixed_ip or not fixed_port_id:
                LOG.debug('\n db  skip floatingip=%s', floatingip)
                continue
            yield floatingip

    def get_sync_data_for_rule(self, context, rule):
        label = metering_objs.MeteringLabel.get_object(
            context, id=rule['metering_label_id'])

        ts = timeutils.utcnow_ts()
        # Do not support label.shared:
        routers_dict = {}
        ip_version = 'ipv6'
        ip_addr = rule['remote_ip_prefix'].split(';')[0]
        if ip_addr:
            if netaddr.IPNetwork(ip_addr).version == 4:
                ip_version = 'ipv4'

        if label.floatingip_id is None:
            LOG.error('\n Floatingip should not be None in label rule=%s',
                      rule)
            return list(routers_dict.values())

        filters = {'id': [label['router_id']]}
        routers = l3_obj.Router.get_objects(context, **filters)

        if not routers:
            LOG.error('\n Router may be has been removed from label rule=%s',
                      rule)

        for router in routers:
            router_dict = routers_dict.get(router['id'],
                                           self._make_router_dict(router))
            data = {'id': label['id'], 'ipversion': ip_version,
                    'type': label['type'], 'rules': rule}
            router_dict[constants.METERING_LABEL_KEY].append(data)
            self._process_address_groups(context, router_dict, rule)
            routers_dict[router['id']] = router_dict
        LOG.debug('\n Sync data for meter_rule escap:%s',
                  timeutils.utcnow_ts() - ts)

        return list(routers_dict.values())

    def _process_address_groups(self, context, router_dict, rule):
        if constants.ADDRESS_GROUP_KEY not in router_dict.keys():
            router_dict[constants.ADDRESS_GROUP_KEY] = []
        address_group_id = rule.get('address_group_id')
        if not address_group_id:
            return
        for ag in router_dict[constants.ADDRESS_GROUP_KEY]:
            if address_group_id == ag.get('id'):
                return
        ag_info = self._core_plugin.get_address_group(context.elevated(),
                                                      address_group_id)
        all_ags = router_dict[constants.ADDRESS_GROUP_KEY]
        all_ags.append(ag_info)
        router_dict[constants.ADDRESS_GROUP_KEY] = all_ags

    def get_sync_full_data_metering_by_labelid(self, context, label_id,
                                               action=None):
        filters = {'id': [label_id]}
        labels = metering_objs.MeteringLabel.get_objects(
            context, **filters)
        if not labels:
            return

        label = labels[0]
        router_id = label['router_id']
        return self.get_sync_full_data_metering(context, [router_id], labels,
                                                action)

    def get_sync_full_data_metering_by_agents(self, context, l3_plugin,
                                              agents):
        router_ids = []
        for agent in agents:
            router_ids = l3_plugin.list_router_ids_on_host(context, agent.host)
        labels = []

        action = "hostsync"
        return self.get_sync_full_data_metering(context, router_ids, labels,
                                                action)

    def get_router_host_location(self, context, router_id):
        if not router_id:
            LOG.error("Get router id=%s is None.", router_id)
            return []

        query_agent_ids = context.session.query(
            routerl3binding.RouterL3AgentBinding.l3_agent_id)
        query_agent_ids = query_agent_ids.filter(
            routerl3binding.RouterL3AgentBinding.router_id == router_id).all()

        query_hosts = context.session.query(agent_model.Agent.host)
        host_list = []
        for agent_id in query_agent_ids:
            hostname = query_hosts.filter(
                agent_model.Agent.id == agent_id[0]).all()
            hostname = hostname[0][0]
            host_list.append(hostname)

        return host_list

    def get_sync_full_data_metering_by_spec_rouerid(self, context,
                                                    router_id, host=None):

        if router_id is None:
            LOG.error("Input cmd router id is None")
            return

        router_ids = [router_id]

        rfilters = {'router_id': router_ids}
        labels = metering_objs.MeteringLabel.get_objects(
            context, **rfilters)
        LOG.debug("labels=%s bind with router=%s .", labels, router_id)
        # clean  all label and rules
        exist_label = True
        if not labels:
            exist_label = False

        return (exist_label, self.get_sync_full_data_metering(context,
                                                              router_ids,
                                                              labels))

    def _check_meter_data_is_normal(self, context, label, router, l3plugin):
        if label.floatingip_id is None:
            LOG.error('meter label fip should not be None %s', label['id'])
            return None

        if not router['admin_state_up']:
            LOG.warning('Router %s admin state is False in labe %s',
                        router['id'], label['id'])
            return None

        if router['id'] != label['router_id']:
            LOG.warning('Router id %s is not equal router id %s in label',
                        router['id'], label['router_id'])
            return None

        fip_filters = {'id': label.floatingip_id}
        floatingips = l3plugin.get_floatingips(context, fip_filters)

        floatingip = None
        fip = [fip for fip in floatingips]
        if fip:
            floatingip = fip[0]

        if floatingip is None:
            LOG.warning('Fip may has been deleted from label %s', label)
            return None

        if floatingip['router_id'] and (
                router['id'] != floatingip['router_id']):
            LOG.warning('Router in label %s is not consistent with in FIP',
                        label)
            return None
        return floatingip

    def _get_vm_uid_by_port_id(self, context, port_id):
        port = self._core_plugin.get_port(context, port_id)
        return port.get('device_id')

    def _build_meterinfo_for_portforwardig(self, context, data, tree):
        # build n portforwarding
        pf_objs = pf.PortForwarding.get_objects(context,
                                                floatingip_id=data['FIP_uuid'])
        for pf_obj in pf_objs:
            pf_data = copy.deepcopy(data)
            pf_data['vm_ip'] = (str(pf_obj.internal_ip_address))
            internal_port_id = pf_obj.internal_port_id
            pf_data['vm_portid'] = internal_port_id
            pf_data['vm_uuid'] = self._get_vm_uid_by_port_id(context,
                                                             internal_port_id)
            tree.append(pf_data)

    def get_sync_full_data_metering(self, context, routers_ids, labels,
                                    action=None):
        if routers_ids is None and labels is None:
            LOG.error("\n Input none not allowed.")
            return []
        ts = timeutils.utcnow_ts()
        filters = {'id': routers_ids}
        local_routers = l3_obj.Router.get_objects(context, **filters)

        routers_dict = {}
        for router in local_routers:
            router_dict = routers_dict.get(router['id'],
                                           self._make_router_dict(router))
            if action == "hostsync":
                # rfilters = {'router_id': router['id']}
                # do not use input, use query result
                labels = metering_objs.MeteringLabel.get_objects(
                    context, router_id=router['id'])
            elif action == 'delete':
                for label in labels:
                    data = {'id': label['id'],
                            'ipversion': label['ipversion'],
                            'FIP_uuid': label.floatingip_id,
                            'FIP_ip': label.name,
                            'type': label['type'],
                            'vm_ip': '',
                            'vm_uuid': '',
                            'vm_portid': '',
                            'rules': self._get_metering_rules_dict(label)}
                    router_dict[constants.METERING_LABEL_KEY].append(data)
                    routers_dict[router['id']] = router_dict
                return list(routers_dict.values())
            for label in labels:
                floatingip = self._check_meter_data_is_normal(
                    context, label, router, self.l3plugin)
                if not floatingip:
                    continue
                fip_type = floatingip['fip_type']
                fip_addr = floatingip['floating_ip_address']
                fip_id = label.floatingip_id
                ipversion = self.get_version_for_ip_address(fip_addr)
                rules = self._get_metering_rules_dict(label)
                for rule in rules:
                    self._process_address_groups(context, router_dict, rule)
                data = {'id': label['id'],
                        'ipversion': label['ipversion'],
                        'FIP_uuid': fip_id,
                        'FIP_ip': label.name,
                        'type': label['type'],
                        'vm_ip': '',
                        'vm_uuid': '',
                        'vm_portid': '',
                        'rules': rules}
                fip_uuid = floatingip.get('id', None)
                floating_ip_address = floatingip.get('floating_ip_address',
                                                     None)
                data['FIP_uuid'] = fip_uuid
                data['FIP_ip'] = floating_ip_address
                if fip_type == lib_constants.FLOATINGIP_TYPE_FIP:
                    router_dict = self._get_floatingip_meter(
                        context, router_dict, floatingip, data, action,
                        ipversion)

                elif fip_type == lib_constants.FLOATINGIP_TYPE_ECS_IPv6:
                    router_dict = self._get_ecs_fip_meter(
                        context, fip_id, fip_addr, router_dict, ipversion,
                        data)
                routers_dict[router['id']] = router_dict
        LOG.debug("Get sync full data metering:%s", timeutils.utcnow_ts() - ts)
        return list(routers_dict.values())

    def _get_floatingip_meter(self, context, router_dict, floatingip, data,
                              action, ipversion):
        if ipversion == 'ipv4':
            fixed_ip_address = floatingip.get('fixed_ip_address', None)
            data['vm_portid'] = floatingip.get('port_id', None)
            if data['type'] == constants.METERING_TYPE_PF_EIP:
                if action == 'delete':
                    router_dict[
                        constants.METERING_LABEL_KEY].append(data)
                else:
                    self._build_meterinfo_for_portforwardig(
                        context, data,
                        router_dict[constants.METERING_LABEL_KEY])
            elif data['type'] == constants.METERING_TYPE_SNAT_EIP or \
                    data['type'] == constants.METERING_TYPE_MULTI_EIP:
                router_dict[constants.METERING_LABEL_KEY].append(data)
            else:
                # This is ip mapping.
                port_details = floatingip.get('port_details')
                if port_details:
                    device_id = port_details['device_id']
                    data['vm_ip'] = fixed_ip_address
                    data['vm_uuid'] = device_id
                router_dict[constants.METERING_LABEL_KEY].append(data)
        return router_dict

    def _get_ecs_fip_meter(self, context, fip_id, fip_addr, router_dict,
                           ipversion, data):
        if ipversion == 'ipv6':
            fip_obj = l3_obj.FloatingIP.get_object(context, id=fip_id)
            if fip_obj:
                vm_port_id = fip_obj.floating_port_id
                port_details = self._core_plugin.get_port(context, vm_port_id)
                data['vm_uuid'] = port_details['device_id']
                data['vm_ip'] = fip_addr
                data['vm_portid'] = vm_port_id
                router_dict[constants.METERING_LABEL_KEY].append(data)
        return router_dict

    # for deleting meter label
    def get_label_uuid_by_floatingipid(self, context, floatingip_id,
                                       project_id):
        LOG.debug('\n get_label_uuid_by_floatingipid floatingip_id=%s '
                  'project_id=%s ', floatingip_id, project_id)
        query = context.session.query(metering_db.MeteringLabel)
        query = query.filter(
            and_(metering_db.MeteringLabel.floatingip_id == floatingip_id,
                 metering_db.MeteringLabel.project_id == project_id))
        q = query.first()
        if q:
            LOG.debug('\n Found label uuid=%s by floatingipid in db', q['id'])
            return q['id']

        LOG.debug('\n Not found label uuid with FIP=%s  in db', floatingip_id)
        return None

    def _get_metering_label_rule_ids(self, context, filters=None, fields=None,
                                     sorts=None, limit=None, marker=None,
                                     page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        metering_label_rules = metering_objs.MeteringLabelRule.get_objects(
            context, _pager=pager, **filters)
        return [mlr['id']
                for mlr in metering_label_rules]

    def get_label_rule_uuid_by_floatingipid(self, context, floatingip_id,
                                            project_id):
        LOG.debug('\n Get label rule uuid by floatingip_id=%s'
                  ' project_id=%s ', floatingip_id, project_id)
        query = context.session.query(metering_db.MeteringLabel)
        query = query.filter(
            and_(metering_db.MeteringLabel.floatingip_id == floatingip_id,
                 metering_db.MeteringLabel.project_id == project_id))
        q = query.first()
        if not q:
            LOG.warning('\n Not found floatingip_id=%s tenant=%s '
                        'in meter_label table', floatingip_id, project_id)
            return []

        filters = {'metering_label_id': q['id']}
        rule_ids = self._get_metering_label_rule_ids(context, filters)
        LOG.debug('\n rule_ids=%s in db', rule_ids)

        return rule_ids

    def clean_metering_iptable(self, context, router_id):
        return

    def _make_metering_vpc_dict(self, metering_vpc, fields=None):
        res = {'id': metering_vpc['id'],
               'description': metering_vpc['description'],
               'router_id': metering_vpc['router_id'],
               'destination_ip': metering_vpc['destination_ip'],
               'project_id': metering_vpc['project_id'],
               'dest_device_uuid': metering_vpc['dest_device_uuid'],
               'network_id': [metering_vpc['network_id']],
               'ha': metering_vpc['ha'],
               'hostname': metering_vpc['hostname'],
               'type': metering_vpc['type']
               }
        return db_utils.resource_fields(res, fields)

    @log_helpers.log_method_call
    def create_metering_vpc(self, context, metering_vpc):
        try:
            model_query.get_by_id(
                context, l3_models.Router, metering_vpc['router_id'])
        except exc.NoResultFound:
            raise metering_exc.MeteringVpcRouterNotFound(
                router_id=metering_vpc['router_id'])
        network_id = metering_vpc['network_id']
        if network_id:
            if isinstance(network_id, list):
                network_id = network_id[0]
            try:
                model_query.get_by_id(
                    context, models_v2.Network, network_id)
            except exc.NoResultFound:
                raise metering_exc.MeteringVpcNetWorkNotFound(
                    network_id=network_id)

        metering_vpc['ha'], metering_vpc['hostname'] = \
            self._get_router_agent_value(context, metering_vpc['router_id'])
        metering_vpc_obj = metering_objs.MeteringVpc(
            context,
            id=uuidutils.generate_uuid(),
            description=metering_vpc['description'],
            router_id=metering_vpc['router_id'],
            destination_ip=metering_vpc['destination_ip'],
            project_id=metering_vpc['project_id'],
            dest_device_uuid=metering_vpc['dest_device_uuid'],
            network_id=network_id,
            ha=metering_vpc['ha'],
            hostname=metering_vpc['hostname'],
            type=metering_vpc['type']
        )
        metering_vpc_obj.create()
        return self._make_metering_vpc_dict(metering_vpc_obj)

    def _get_router_agent_value(self, context, router_id):
        # get router ha info
        query_extea = context.session.query(
            l3_attrs.RouterExtraAttributes.ha)
        query_extea = query_extea.filter(
            l3_attrs.RouterExtraAttributes.router_id == router_id).all()
        ha = query_extea[0][0]
        # get hostname info
        query_agent_ids = context.session.query(
            routerl3binding.RouterL3AgentBinding.l3_agent_id)
        query_agent_ids = query_agent_ids.filter(
            routerl3binding.RouterL3AgentBinding.router_id == router_id).all()

        query_hosts = context.session.query(agent_model.Agent.host)
        host_list = []
        for agent_id in query_agent_ids:
            hostname = query_hosts.filter(
                agent_model.Agent.id == agent_id[0]).all()
            hostname = hostname[0][0]
            host_list.append(hostname)
        hostnames = ','.join([str(x) for x in host_list])
        return ha, hostnames

    def _get_metering_vpc(self, context, metering_vpc):
        if '+' not in metering_vpc:
            vpc = metering_objs.MeteringVpc.get_object(
                context, id=metering_vpc)
            vpcs = vpc
        else:
            vpc_list = tuple(metering_vpc.split('+'))
            router_id = vpc_list[0]
            destination_ip = vpc_list[1]
            if destination_ip != '':
                filters = {'router_id': router_id,
                           'destination_ip': destination_ip}
                vpcs = self.get_metering_vpcs(context, filters=filters)
            else:
                filters = {'router_id': router_id}
                vpcs = self.get_metering_vpcs(context, filters=filters)
            if vpcs:
                vpc = vpcs[0]
        if not vpcs:
            raise metering_exc.MeteringVpcNotFound(
                metering_vpc_id=metering_vpc)
        return vpc

    @log_helpers.log_method_call
    def delete_metering_vpc(self, context, metering_vpc_id):
        vpc_db = metering_objs.MeteringVpc.get_object(context,
                                                      id=metering_vpc_id)
        deleted = metering_objs.MeteringVpc.delete_objects(context,
                                                           id=metering_vpc_id)
        if not deleted:
            LOG.warning("Deleting a non-existing metering vpc.")
            raise metering_exc.MeteringVpcNotFound(
                metering_vpc_id=metering_vpc_id)
        return vpc_db

    @log_helpers.log_method_call
    def get_metering_vpc(self, context, metering_vpc, fields=None):
        return self._make_metering_vpc_dict(
            self._get_metering_vpc(context, metering_vpc), fields)

    @log_helpers.log_method_call
    def get_metering_vpcs(self, context, filters=None, fields=None,
                          sorts=None, limit=None, marker=None,
                          page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        metering_vpcs = metering_objs.MeteringVpc.get_objects(context,
                                                              _pager=pager,
                                                              **filters)
        vpcs = [self._make_metering_vpc_dict(ml) for ml in metering_vpcs]
        return vpcs

    @log_helpers.log_method_call
    def get_host_dhcp_networks(self, context, host):
        ndab_query = context.session.query(
            ndab_models.NetworkDhcpAgentBinding.network_id)
        ndab_query = ndab_query.join(
            agent_model.Agent,
            agent_model.Agent.id ==
            ndab_models.NetworkDhcpAgentBinding.dhcp_agent_id)
        ndab_query = ndab_query.filter(and_(
            agent_model.Agent.host == host,
            agent_model.Agent.binary == 'neutron-dhcp-agent'))
        quert_list = ndab_query.all()
        return [q.network_id for q in quert_list]

    @log_helpers.log_method_call
    def set_vpc_default_network(self, context, id, router_id):
        port_query = context.session.query(models_v2.Port.network_id)
        port_query = port_query.join(
            l3_models.RouterPort,
            l3_models.RouterPort.port_id == models_v2.Port.id)
        port_query = port_query.filter(and_(
            l3_models.RouterPort.router_id == router_id,
            l3_models.RouterPort.port_type.in_([
                lib_constants.DEVICE_OWNER_ROUTER_INTF,
                lib_constants.DEVICE_OWNER_HA_REPLICATED_INT])))
        port = port_query.first()
        if port:
            network_id = port.network_id
            with db_api.context_manager.writer.using(context):
                metering_vpc = metering_objs.MeteringVpc.get_object(
                    context, id=id)
                metering_vpc.update_fields({'network_id': network_id})
                metering_vpc.update()
            return network_id

    @log_helpers.log_method_call
    def router_interface_remove(self, context, router_id, network_id):
        with db_api.context_manager.writer.using(context):
            metering_vpcs = metering_objs.MeteringVpc.get_objects(
                context, router_id=router_id, network_id=network_id)
            for metering_vpc in metering_vpcs:
                metering_vpc.update_fields({'network_id': ''})
                metering_vpc.update()

    def sync_metering_vpc_routers(self, context, meter_vpc):
        router_query = context.session.query(
            l3_attrs.RouterExtraAttributes.router_id)
        router = router_query.filter(
            l3_attrs.RouterExtraAttributes.router_id ==
            meter_vpc['router_id']).all()
        router = router[0][0]
        return router

    def update_metering_vpc_db(self, context, id, metering_vpc):
        with context.session.begin(subtransactions=True):
            metering_vpc_obj = self._get_metering_vpc(context, id)
            if 'description' in metering_vpc:
                metering_vpc_obj.description = metering_vpc['description']
            if 'destination_ip' in metering_vpc:
                metering_vpc_obj.destination_ip = \
                    metering_vpc['destination_ip']
            if 'dest_device_uuid' in metering_vpc:
                metering_vpc_obj.dest_device_uuid = \
                    metering_vpc['dest_device_uuid']
            if 'network_id' in metering_vpc:
                metering_vpc_obj.network_id = metering_vpc['network_id']
            if 'hostname' in metering_vpc:
                metering_vpc_obj.hostname = metering_vpc['hostname']
            if 'type' in metering_vpc:
                metering_vpc_obj.type = metering_vpc['type']
            metering_vpc_obj.update()
            return self._make_metering_vpc_dict(metering_vpc_obj)

    def metering_router_callback(self, context, host):
        # Get l3_agent_id by hostname
        query_hosts = context.session.query(agent_model.Agent.id)
        l3_agent = query_hosts.filter(
            agent_model.Agent.binary == 'neutron-l3-agent',
            agent_model.Agent.host == host)
        l3_agent_id = l3_agent[0][0]

        routerbinding = context.session.query(
            l3ha.L3HARouterAgentPortBinding)
        router_bindings = routerbinding.filter(
            l3ha.L3HARouterAgentPortBinding.l3_agent_id == l3_agent_id).all()

        query_extea = context.session.query(l3_attrs.RouterExtraAttributes.ha)

        routers_info = []
        for router in router_bindings:
            r_info = {}
            r_info['id'] = router.router_id
            r_info['status'] = router.state
            ha = query_extea.filter(
                l3_attrs.RouterExtraAttributes.router_id ==
                router.router_id).all()
            r_info['ha'] = ha[0][0]
            routers_info.append(r_info)

        return routers_info

    @log_helpers.log_method_call
    def update_meter_label_db_only(self, context, floatingip_id, project_id,
                                   target_ip):
        with db_api.context_manager.writer.using(context):
            metering_labels = metering_objs.MeteringLabel.get_objects(
                context, floatingip_id=floatingip_id, project_id=project_id)
            for metering_label in metering_labels:
                metering_label.update_fields({'description': target_ip})
                metering_label.update()

    def get_version_for_ip_address(self, ip_address):
        ip_version = 'ipv4'
        if netaddr.IPNetwork(ip_address).version == 6:
            ip_version = 'ipv6'
        return ip_version
