#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_log import log as logging


LOG = logging.getLogger(__name__)


class L3RpcDriver(object):

    def __init__(self):
        self.name = "l3_agent"
        self.l3_plugin = directory.get_plugin(constants.L3)

    def _notify_route_table_update(self, context, router_id):
        centralized_agent_list = self.l3_plugin.list_l3_agents_hosting_router(
            context, router_id)['agents']
        for agent in centralized_agent_list:
            LOG.debug("Notify router %s update for elastic snat on host %s",
                      router_id, agent['host'])
            self.l3_plugin.l3_rpc_notifier.routers_updated_on_host(
                    context, [router_id], agent['host'])

    def create_route_table(self, context, route_table):
        self._notify_route_table_update(context, route_table.router_id)

    def update_route_table(self, context, route_table):
        self._notify_route_table_update(context, route_table.router_id)

    def delete_route_table(self, context, route_table):
        self._notify_route_table_update(context, route_table.router_id)
