#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import copy

import netaddr

import mock
from neutron_lib import constants
from oslo_config import cfg
from oslo_utils import netutils
import six

from neutron.agent.common import ovs_lib
from neutron.agent import firewall
from neutron.agent.linux.openvswitch_firewall import constants as ovsfw_consts
from neutron.agent.linux.openvswitch_firewall import stateless_firewall
from neutron.conf.agent import securitygroups_rpc as sg_cfg
from neutron.plugins.ml2.drivers.openvswitch.agent.common import (constants
    as ovs_constants)
from neutron.tests.unit.plugins.ml2.drivers.openvswitch.agent \
    import ovs_test_base

IPv4 = "IPv4"
IPv6 = "IPv6"

FAKE_PREFIX = {IPv4: '10.0.0.0/24',
               IPv6: 'fe80::/48'}
FAKE_IP = {IPv4: '********',
           IPv6: 'fe80::1'}

FAKE_SGID = 'fake_sgid'
OTHER_SGID = 'other_sgid'
SEGMENTATION_ID = "1402"
TAG_ID = 1
DEFAULT_MAC = 'fa:16:3e:12:34:56'

# List of protocols.
PROTOCOLS = {IPv4: {'tcp': 'eth_type=0x0800,ip_proto=6',
                    'udp': 'eth_type=0x0800,ip_proto=17',
                    'ip': 'eth_type=0x0800',
                    'icmp': 'eth_type=0x0800,ip_proto=1',
                    'igmp': 'eth_type=0x0800,ip_proto=2',
                    'arp': 'arp',
                    'sctp': 'eth_type=0x0800,ip_proto=132'},
             IPv6: {'tcp': 'eth_type=0x86dd,ip_proto=6',
                    'udp': 'eth_type=0x86dd,ip_proto=17',
                    'ip': 'eth_type=0x86dd',
                    'ipv6': 'eth_type=0x86dd',
                    'icmp': 'eth_type=0x86dd,ip_proto=58',
                    'icmpv6': 'eth_type=0x86dd,ip_proto=58',
                    'arp': 'arp',
                    'sctp': 'eth_type=0x86dd,ip_proto=132'}}
PROTOCOLS_DEFAULT_PRIO = {'tcp': 3010,
                          'udp': 3010,
                          'sctp': 3010,
                          'ip': 3000,
                          'ipv6': 3000,
                          'icmp': 3010}
PROTOCOLS_LEARN_ACTION_PRIO = {'tcp': 3000,
                               'udp': 3000,
                               'sctp': 3000,
                               'ip': 3000,
                               'icmp': 3000}
PROTOCOLS_DEST = {'tcp': 'NXM_OF_TCP_DST[]=NXM_OF_TCP_SRC[],',
                  'udp': 'NXM_OF_UDP_DST[]=NXM_OF_UDP_SRC[],',
                  'sctp': 'OXM_OF_SCTP_DST[]=OXM_OF_SCTP_SRC[],',
                  'ip': '',
                  'ipv6': '',
                  'icmp': ''}

PROTOCOLS_SRC = {'tcp': 'NXM_OF_TCP_SRC[]=NXM_OF_TCP_DST[],',
                 'udp': 'NXM_OF_UDP_SRC[]=NXM_OF_UDP_DST[],',
                 'sctp': 'OXM_OF_SCTP_SRC[]=OXM_OF_SCTP_DST[],',
                 'ip': '',
                 'ipv6': '',
                 'icmp': ''}

IDLE_TIMEOUT = stateless_firewall.LEARN_IDLE_TIMEOUT
HARD_TIMEOUT = stateless_firewall.LEARN_HARD_TIMEOUT

# OpenFlow Table IDs
OF_ZERO_TABLE = stateless_firewall.OF_ZERO_TABLE
OF_TRANSIENT_TABLE = stateless_firewall.OF_TRANSIENT_TABLE
OF_SELECT_TABLE = stateless_firewall.OF_SELECT_TABLE
OF_EGRESS_TABLE = stateless_firewall.OF_EGRESS_TABLE
OF_NO_SG_VLAN_MATCH = stateless_firewall.OF_NO_SG_VLAN_MATCH
OF_ACCEPT_OR_INGRESS_TABLE = stateless_firewall.OF_ACCEPT_OR_INGRESS_TABLE
OF_SEC_EXT_TABLE = stateless_firewall.OF_SEC_EXT_TABLE

FLOW_LOG_CLASSIFY_TABLE = ovs_constants.FLOW_LOG_CLASSIFY_TABLE

OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE = (
    stateless_firewall.OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)

DEFAULT_BRIDGE_MAPPINGS = ['default:br-test']
COOKIE = 1
PATCH_OF_PORT = 1111
DEVICE_OF_PORT = 1

MIN_PRI = 1000
PRIORITY_GAP = 5
BASE_RULE_PRI = MIN_PRI + PRIORITY_GAP
IP_PRI = BASE_RULE_PRI
TCP_UDP_PRI = BASE_RULE_PRI + 1
ICMP_PRI = BASE_RULE_PRI + 2
SPEC_PROTOCOL_PRI = BASE_RULE_PRI + 3


class BaseOVSStatelessFirewallDriverTestCase(ovs_test_base.OVSRyuTestBase):
    def setUp(self):
        super(BaseOVSStatelessFirewallDriverTestCase, self).setUp()
        cfg.CONF.register_opts(sg_cfg.security_group_opts, 'SECURITYGROUP')
        conn_patcher = mock.patch('neutron.agent.ovsdb.impl_idl._connection')
        conn_patcher.start()
        # Don't attempt to connect to ovsdb
        mock.patch('neutron.agent.ovsdb.api.from_config').start()
        # Don't trigger iptables -> ovsfw migration
        mock.patch(
            'neutron.agent.linux.openvswitch_firewall.iptables.Helper').start()
        self.addCleanup(conn_patcher.stop)
        int_br = self.br_int_cls("br-int")
        self.firewall = stateless_firewall.OVSStatelessFirewallDriver(int_br)


class OVSStatelessFirewallTestCase(BaseOVSStatelessFirewallDriverTestCase):
    def setUp(self):
        super(OVSStatelessFirewallTestCase, self).setUp()
        # NOTE(ralonsoh): by default,
        #                 OVSStatelessFirewallDriver._deferred = False,
        #                 therefore neutron.agent.common.ovs_lib.OVSBridge is
        #                 used.
        self._mock_add_flow = \
            mock.patch.object(ovs_lib.OVSBridge, "add_flow")
        self.mock_add_flow = self._mock_add_flow.start()
        self._mock_delete_flows = \
            mock.patch.object(ovs_lib.OVSBridge, "delete_flows")
        self.mock_delete_flows = self._mock_delete_flows.start()
        self._mock_get_vif_port_by_id = \
            mock.patch.object(ovs_lib.OVSBridge, "get_vif_port_by_id")
        self.mock_get_vif_port_by_id = self._mock_get_vif_port_by_id.start()
        self._mock_db_get_val =\
            mock.patch.object(ovs_lib.OVSBridge, "db_get_val")
        self.mock_db_get_val = self._mock_db_get_val.start()

        self._mock_get_port_ofport = \
            mock.patch.object(ovs_lib.OVSBridge, "get_port_ofport")
        self.mock_get_port_ofport = self._mock_get_port_ofport.start()
        self.mock_get_port_ofport.return_value = PATCH_OF_PORT

        # Create a fake port.
        self.fake_port_1 = self._fake_port(name='tapfake_dev_1')
        # Mock the VifPort.
        self.mock_get_vif_port_by_id.return_value = \
            self._fake_vifport(self.fake_port_1)
        self.mport = ['0x0020/0xffe0', '0x0010/0xfff0', '0x000c/0xfffc',
                      '0x000a/0xfffe', '0x0040/0xffe0', '0x0060/0xfffc',
                      '0x0064']
        self.learn_idle_timeout = cfg.CONF.OVS.firewall_learn_idle_timeout
        self.learn_hard_timeout = cfg.CONF.OVS.firewall_learn_hard_timeout

    def tearDown(self):
        super(OVSStatelessFirewallTestCase, self).tearDown()
        self._mock_add_flow.stop()
        self._mock_delete_flows.stop()
        self._mock_get_vif_port_by_id.stop()
        self._mock_db_get_val.stop()

    def _fake_port(self, name,
                   ofport=DEVICE_OF_PORT,
                   device='tapfake_dev_1',
                   mac=DEFAULT_MAC,
                   sg_id=FAKE_SGID,
                   zone_id=1,
                   allowed_address_pairs=None,
                   network_type='vlan',
                   device_owner=''):
        return {'name': name,
                'ofport': ofport,
                'device': device,
                'mac_address': mac,
                'vinfo': {'tag': zone_id,
                          'network_type': network_type,
                          'segmentation_id': SEGMENTATION_ID,
                          'physical_network': 'default'},
                'network_id': 'fake_net',
                'fixed_ips': [FAKE_IP[IPv4],
                              FAKE_IP[IPv6]],
                'security_groups': [sg_id],
                'security_group_source_groups': [sg_id],
                'allowed_address_pairs': allowed_address_pairs or [],
                'device_owner': device_owner}

    def _fake_sg_rule_for_ethertype(self, ethertype, remote_group):
        return {'direction': 'ingress', 'remote_group_id': remote_group,
                'ethertype': ethertype}

    def _fake_sg_rules(self, sg_id=FAKE_SGID, remote_groups=None):
        remote_groups = remote_groups or {IPv4: [FAKE_SGID],
                                          IPv6: [FAKE_SGID]}
        rules = []
        for ip_version, remote_group_list in six.iteritems(remote_groups):
            for remote_group in remote_group_list:
                rules.append(self._fake_sg_rule_for_ethertype(ip_version,
                                                              remote_group))
        return {sg_id: rules}

    def _fake_sg_members(self, sg_ids=None):
        return {sg_id: copy.copy(FAKE_IP)
                for sg_id in (sg_ids or [FAKE_SGID])}

    def _fake_vifport(self, port):
        return ovs_lib.VifPort(port['name'],
                               port['ofport'],
                               port['device'],
                               port['mac_address'],
                               "br-%s" % port['device'])

    def _write_ip_src_dst(self, eth_type):
        # Source and destination IPs.
        if eth_type == IPv4:
            ip_dst = "NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],"
            ip_src = "NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],"
        else:
            ip_dst = "NXM_NX_IPV6_DST[]=NXM_NX_IPV6_SRC[],"
            ip_src = "NXM_NX_IPV6_SRC[]=NXM_NX_IPV6_DST[],"
        return ip_src, ip_dst

    def _write_proto(self, eth_type, protocol=None):
        return PROTOCOLS[eth_type][protocol]

    def _learn_egress_actions(self, protocol, ethertype, priority=None,
                       icmp_type=None, icmp_code=None):
        protocol_str = self._write_proto(ethertype, protocol)
        ip_src, ip_dst = self._write_ip_src_dst(ethertype)
        if not priority:
            priority = PROTOCOLS_DEFAULT_PRIO[protocol]
        port_destination = PROTOCOLS_DEST[protocol]
        port_source = PROTOCOLS_SRC[protocol]
        icmp_type_str = ""
        if icmp_type:
            icmp_type_str = 'icmp_type=%s,' % icmp_type
        icmp_code_str = ""
        if icmp_code:
            icmp_code_str = 'icmp_code=%s,' % icmp_code
        mac_match = "NXM_OF_ETH_DST[]=NXM_OF_ETH_SRC[],"
        if cfg.CONF.AGENT.enable_learn_mac_match_all:
            mac_match = ("NXM_OF_ETH_SRC[]=NXM_OF_ETH_DST[],"
                         "NXM_OF_ETH_DST[]=NXM_OF_ETH_SRC[],")

        if protocol == constants.PROTO_NAME_TCP:
            learn_idle_timeout = self.learn_idle_timeout
            learn_hard_timeout = self.learn_hard_timeout
        else:
            learn_idle_timeout = IDLE_TIMEOUT
            learn_hard_timeout = HARD_TIMEOUT

        output_str = 'learn(cookie=%(cookie)s,' \
                     'table=%(table)s,' \
                     'priority=%(priority)s,' \
                     'idle_timeout=%(idle_timeout)s,' \
                     'hard_timeout=%(hard_timeout)s,' \
                     '%(protocol)s,' \
                     "%(mac_match)s" \
                     '%(ip_src)s' \
                     '%(ip_dst)s' \
                     '%(port_destination)s' \
                     '%(port_source)s' \
                     '%(icmp_type)s' \
                     '%(icmp_code)s' \
                     'NXM_OF_VLAN_TCI[0..11],' \
                     'load:NXM_OF_IN_PORT[0..15]->NXM_NX_REG9[0..15],' \
                     'load:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG10[0..11])' \
                     % {'cookie': self.firewall.learn_cookie,
                        'table': OF_ACCEPT_OR_INGRESS_TABLE,
                        'priority': priority,
                        'idle_timeout': learn_idle_timeout,
                        'hard_timeout': learn_hard_timeout,
                        'protocol': protocol_str,
                        'mac_match': mac_match,
                        'ip_src': ip_src,
                        'ip_dst': ip_dst,
                        'port_destination': port_destination,
                        'port_source': port_source,
                        'icmp_type': icmp_type_str,
                        'icmp_code': icmp_code_str,
                        'r_table': OF_ACCEPT_OR_INGRESS_TABLE}
        return output_str

    def _learn_ingress_actions(self, protocol, ethertype, priority=None,
                       icmp_type=None, icmp_code=None, ofport=1):
        protocol_str = PROTOCOLS[ethertype][protocol]
        ip_src, ip_dst = self._write_ip_src_dst(ethertype)
        if not priority:
            priority = PROTOCOLS_DEFAULT_PRIO[protocol]
        port_destination = PROTOCOLS_DEST[protocol]
        port_source = PROTOCOLS_SRC[protocol]
        icmp_type_str = ""
        if icmp_type:
            icmp_type_str = 'icmp_type=%s,' % icmp_type
        icmp_code_str = ""
        if icmp_code:
            icmp_code_str = 'icmp_code=%s,' % icmp_code
        mac_match = "NXM_OF_ETH_SRC[]=NXM_OF_ETH_DST[],"
        if cfg.CONF.AGENT.enable_learn_mac_match_all:
            mac_match = ("NXM_OF_ETH_SRC[]=NXM_OF_ETH_DST[],"
                         "NXM_OF_ETH_DST[]=NXM_OF_ETH_SRC[],")

        if protocol in [constants.PROTO_NAME_TCP, constants.PROTO_NAME_SCTP]:
            learn_idle_timeout = self.learn_idle_timeout
            learn_hard_timeout = self.learn_hard_timeout
        else:
            learn_idle_timeout = IDLE_TIMEOUT
            learn_hard_timeout = HARD_TIMEOUT

        output_str = 'learn(cookie=%(cookie)s,' \
                     'table=%(table)s,' \
                     'priority=%(priority)s,' \
                     'idle_timeout=%(idle_timeout)s,' \
                     'hard_timeout=%(hard_timeout)s,' \
                     '%(protocol)s,' \
                     "%(mac_match)s" \
                     '%(ip_src)s' \
                     '%(ip_dst)s' \
                     '%(port_destination)s' \
                     '%(port_source)s' \
                     '%(icmp_type)s' \
                     '%(icmp_code)s' \
                     'NXM_OF_VLAN_TCI[0..11],' \
                     'load:NXM_OF_IN_PORT[0..15]->NXM_NX_REG9[0..15],' \
                     'load:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG10[0..11]),' \
                     'set_field:%(ofport)d->reg9,' \
                     'move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG10[0..11],' \
                     'move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG6[0..11]' \
                     % {'cookie': self.firewall.learn_cookie,
                        'table': OF_EGRESS_TABLE,
                        'priority': priority,
                        'idle_timeout': learn_idle_timeout,
                        'hard_timeout': learn_hard_timeout,
                        'protocol': protocol_str,
                        'mac_match': mac_match,
                        'ip_src': ip_src,
                        'ip_dst': ip_dst,
                        'port_destination': port_destination,
                        'port_source': port_source,
                        'icmp_type': icmp_type_str,
                        'icmp_code': icmp_code_str,
                        'ofport': ofport}
        return output_str

    @staticmethod
    def _get_test_port_ipv6_address(port, vif=None):
        addresses = set(port['fixed_ips'])
        if vif:
            mac_addresses = {vif.vif_mac}
        elif port.get('mac_address'):
            mac_addresses = {port.get('mac_address')}
        else:
            mac_addresses = set()
        if port.get('allowed_address_pairs'):
            addresses |= {p['ip_address']
                          for p in port['allowed_address_pairs']}
            mac_addresses |= {p['mac_address']
                              for p in port['allowed_address_pairs']
                              if p.get('mac_address')}

        ipv6_addresses = {ip for ip in addresses
                          if netaddr.IPNetwork(ip).version == 6}
        ipv6_addresses |= {str(netutils.get_ipv6_addr_by_EUI64(
                               constants.IPv6_LLA_PREFIX, mac))
                           for mac in mac_addresses}
        return ipv6_addresses

    def test_prepare_port_filter(self):
        self._test_prepare_port_filter()

    def test_prepare_port_filter_explicitly_egress_direct(self):
        self._test_prepare_port_filter(explicitly_egress_direct=True)

    def test_prepare_port_filter_with_allowed_address_pairs(self):
        self._test_prepare_port_filter(
            allowed_address_pairs=[
                {'ip_address': '***************',
                 'mac_address': 'fa:16:3e:72:05:73'},
                {'ip_address': '***************',
                 'mac_address': 'fa:16:3e:72:05:73'},
                {'ip_address': 'fda7:a5cc:3460:1::f:f',
                 'mac_address': 'fa:16:3e:72:05:73'},
                {'ip_address': 'fda7:a5cc:3460:1::e:e',
                 'mac_address': 'fa:16:3e:ee:ee:ee'},
                {'ip_address': '***************',
                 'mac_address': 'fa:16:3e:ff:ff:ff'}])

    def _test_prepare_port_filter(self, explicitly_egress_direct=False,
                                  allowed_address_pairs=None,
                                  anti_ip_spoof=True,
                                  use_vir_port=False):
        if explicitly_egress_direct:
            cfg.CONF.set_override("explicitly_egress_direct", True,
                                  "AGENT")
            cfg.CONF.set_override("bridge_mappings", DEFAULT_BRIDGE_MAPPINGS,
                                  "OVS")
            if anti_ip_spoof:
                cfg.CONF.set_override("anti_ip_spoof", True, "SECURITYGROUP")
            else:
                cfg.CONF.set_override("anti_ip_spoof", False, "SECURITYGROUP")

        # Setup rules and SG.
        self.firewall.sg_rules = self._fake_sg_rules()
        self.firewall.sg_members = {FAKE_SGID: {
            IPv4: [('********', 'aa:bb:cc:dd:ee:f1'),
                   ('********', 'aa:bb:cc:dd:ee:f2')],
            IPv6: [('fe80::1', 'aa:bb:cc:dd:ee:f3'), ]}}
        self.firewall.pre_sg_members = {}
        self.firewall._enable_multicast = True
        if allowed_address_pairs:
            port = self._fake_port(name='tapfake_dev_1',
                                   allowed_address_pairs=allowed_address_pairs)
        else:
            port = self.fake_port_1

        if use_vir_port:
            vif_port = self._fake_vifport(port)
        else:
            vif_port = None
        self.mock_db_get_val.side_effect = [
            {'net_uuid': "e00e6a6a-c88a-4724-80a7-6368a94241d9",
             'network_type': 'vlan',
             'physical_network': 'default',
             'segmentation_id': SEGMENTATION_ID,
             'tag': TAG_ID},
            TAG_ID,
            'interface',
            {"segmentation_id": SEGMENTATION_ID}]
        self.firewall.prepare_port_filter(port)

        nd_target_addresses = self._get_test_port_ipv6_address(port, vif_port)
        call_del_flows_no_security = []

        call_del_flows_no_security += [
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      table=OF_ZERO_TABLE,
                      icmpv6_type=134,
                      dl_vlan=port['vinfo']['segmentation_id'],
                      dl_dst=port['mac_address'],
                      ),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      table=OF_ZERO_TABLE,
                      icmpv6_type=136,
                      dl_dst=port['mac_address'],
                      dl_vlan=port['vinfo']['segmentation_id'],
                      ),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      table=OF_ZERO_TABLE,
                      icmpv6_type=137,
                      dl_dst=port['mac_address'],
                      dl_vlan=port['vinfo']['segmentation_id'],
                      ),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      table=OF_ZERO_TABLE,
                      icmpv6_type=133,
                      dl_vlan=port['vinfo']['segmentation_id'],
                      dl_dst=port['mac_address'],
                      ),
        ]
        for ipv6_addr in nd_target_addresses:
            call_del_flows_no_security.append(
                mock.call(proto=self._write_proto(IPv6, 'icmp'),
                          table=OF_ZERO_TABLE,
                          icmpv6_type=135,
                          dl_vlan=port['vinfo']['segmentation_id'],
                          nd_target=ipv6_addr,
                          )
            )
        for fixed_ip in port['fixed_ips']:
            if self._ip_version_from_address(fixed_ip) == IPv4:
                call_del_flows_no_security.append(
                    mock.call(
                        table=OF_TRANSIENT_TABLE,
                        proto="eth_type=0x0800",
                        dl_vlan=port['vinfo']['tag'],
                        nw_dst=fixed_ip,
                        dl_dst=port['mac_address'])
                )
                call_del_flows_no_security.append(
                    mock.call(
                        table=ovs_constants.TRAFFIC_MIRROR,
                        proto="eth_type=0x0800",
                        dl_vlan=port['vinfo']['tag'],
                        nw_dst=fixed_ip,
                        dl_dst=port['mac_address'])
                )
                call_del_flows_no_security.append(
                    mock.call(
                        table=ovs_constants.RULES_INGRESS_TABLE,
                        proto="eth_type=0x0800",
                        dl_vlan=port['vinfo']['tag'],
                        nw_dst=fixed_ip,
                        dl_dst=port['mac_address'])
                )
            else:
                call_del_flows_no_security.append(
                    mock.call(
                        table=OF_TRANSIENT_TABLE,
                        proto="eth_type=0x86dd",
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=port['mac_address'])
                )
                call_del_flows_no_security.append(
                    mock.call(
                        table=ovs_constants.TRAFFIC_MIRROR,
                        proto="eth_type=0x86dd",
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=port['mac_address'])
                )
                call_del_flows_no_security.append(
                    mock.call(
                        table=ovs_constants.RULES_INGRESS_TABLE,
                        proto="eth_type=0x86dd",
                        dl_vlan=port['vinfo']['tag'],
                        ipv6_dst=fixed_ip,
                        dl_dst=port['mac_address'])
                )
        calls_del_flows = [mock.call(dl_dst=port["mac_address"],
                                     dl_vlan=1, table=71),
                           mock.call(nw_dst=FAKE_IP[IPv4],
                                     proto='arp',
                                     table=OF_ZERO_TABLE),
                           mock.call(ipv6_dst=FAKE_IP[IPv6],
                                     proto=self._write_proto(IPv6, 'icmp'),
                                     table=OF_ZERO_TABLE)]

        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for ether in firewall.ETH_PROTOCOL_TABLE.keys():
                calls_del_flows.append(
                    mock.call(dl_vlan=TAG_ID,
                              proto=self._write_proto(ether, proto),
                              dl_src=port["mac_address"]))
                calls_del_flows.append(
                    mock.call(dl_vlan=TAG_ID,
                              proto=self._write_proto(ether, proto),
                              dl_dst=port["mac_address"]))
        calls_del_flows.append(
            mock.call(dl_vlan=TAG_ID,
                      dl_src=port["mac_address"]))
        calls_del_flows.append(
            mock.call(dl_vlan=TAG_ID,
                      dl_dst=port["mac_address"]))

        calls_del_flows += [
            mock.call(dl_dst=port["mac_address"],
                      reg6=TAG_ID,
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
            mock.call(dl_dst=port["mac_address"],
                      dl_vlan=TAG_ID,
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
            mock.call(dl_dst='00:00:00:00:00:00/01:00:00:00:00:00',
                      dl_src=port["mac_address"],
                      reg6=TAG_ID,
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
            mock.call(dl_dst='00:00:00:00:00:00/01:00:00:00:00:00',
                      dl_src=port["mac_address"],
                      dl_vlan=TAG_ID,
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)]

        calls_del_flows += call_del_flows_no_security

        calls_del_flows += [
            mock.call(icmpv6_type=133,
                      ipv6_dst='fe80::f816:3eff:fe12:3456',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=134,
                      ipv6_dst='fe80::f816:3eff:fe12:3456',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=135,
                      ipv6_dst='fe80::f816:3eff:fe12:3456',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=136,
                      ipv6_dst='fe80::f816:3eff:fe12:3456',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=137,
                      ipv6_dst='fe80::f816:3eff:fe12:3456',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=133, ipv6_dst='fe80::1',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=134, ipv6_dst='fe80::1',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=135, ipv6_dst='fe80::1',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=136, ipv6_dst='fe80::1',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
            mock.call(icmpv6_type=137, ipv6_dst='fe80::1',
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_TRANSIENT_TABLE),
        ]

        self.mock_delete_flows.assert_has_calls(calls_del_flows,
                                                any_order=True)
        self.firewall._filtered_ports = port

        calls_add_flows = [
            mock.call(proto='arp',
                      actions='strip_vlan,output:%s' % port['ofport'],
                      dl_vlan=TAG_ID,
                      nw_dst='%s' % FAKE_IP[IPv4], priority=100,
                      table=OF_ZERO_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='output:%s' % port['ofport'],
                      icmpv6_type=133, priority=100,
                      ipv6_dst=FAKE_IP[IPv6],
                      table=ovs_constants.TRANSIENT_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='output:%s' % port['ofport'],
                      icmpv6_type=134, priority=100,
                      ipv6_dst=FAKE_IP[IPv6],
                      table=ovs_constants.TRANSIENT_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='output:%s' % port['ofport'],
                      icmpv6_type=135, priority=100,
                      ipv6_dst=FAKE_IP[IPv6],
                      table=ovs_constants.TRANSIENT_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='output:%s' % port['ofport'],
                      icmpv6_type=136, priority=100,
                      ipv6_dst=FAKE_IP[IPv6],
                      table=ovs_constants.TRANSIENT_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='output:%s' % port['ofport'],
                      icmpv6_type=137, priority=100,
                      ipv6_dst=FAKE_IP[IPv6],
                      table=ovs_constants.TRANSIENT_TABLE),
            mock.call(proto='arp',
                      actions='resubmit(,%s)' % ovs_constants.ARP_SPOOF_TABLE,
                      in_port=port['ofport'],
                      priority=90,
                      table=OF_ZERO_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='resubmit(,%s)' % (
                          ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      icmpv6_type=133, priority=90,
                      table=OF_ZERO_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='resubmit(,%s)' % (
                          ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      icmpv6_type=134, priority=90,
                      table=OF_ZERO_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='resubmit(,%s)' % (
                          ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      icmpv6_type=135, priority=90,
                      table=OF_ZERO_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='resubmit(,%s)' % (
                          ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      icmpv6_type=136, priority=90,
                      table=OF_ZERO_TABLE),
            mock.call(proto=self._write_proto(IPv6, 'icmp'),
                      actions='resubmit(,%s)' % (
                          ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      icmpv6_type=137, priority=90,
                      table=OF_ZERO_TABLE),
            mock.call(actions='mod_vlan_vid:%s,load:%s->NXM_NX_REG0[0..11],'
                              'load:0->NXM_NX_REG1[0..11],resubmit(,%s)' %
                              (TAG_ID, 0, ovs_constants.TRAFFIC_MIRROR),
                      priority=50, table=ovs_constants.TRANSIENT_TABLE,
                      in_port=port['ofport'],
                      dl_src=port['mac_address']),
            mock.call(actions='load:%s->NXM_NX_REG0[0..11],'
                              'load:0->NXM_NX_REG1[0..11],resubmit(,%s)' %
                              (TAG_ID, ovs_constants.TRAFFIC_MIRROR),
                      priority=40, table=ovs_constants.TRANSIENT_TABLE,
                      dl_vlan=TAG_ID),
            mock.call(actions='drop', priority=35,
                      table=ovs_constants.TRANSIENT_TABLE),
            mock.call(proto=self._write_proto(IPv4, 'ip'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s),resubmit(,%s)' %
                              (OF_EGRESS_TABLE, FLOW_LOG_CLASSIFY_TABLE),
                      priority=120, table=OF_SELECT_TABLE, dl_vlan=TAG_ID,
                      nw_src='%s' % FAKE_IP[IPv4],
                      in_port=port['ofport']),
            mock.call(proto=self._write_proto(IPv6, 'ip'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s),resubmit(,%s)' %
                              (OF_EGRESS_TABLE, FLOW_LOG_CLASSIFY_TABLE),
                      priority=120, table=OF_SELECT_TABLE, dl_vlan=TAG_ID,
                      ipv6_src='%s' % FAKE_IP[IPv6],
                      in_port=port['ofport']),
            mock.call(priority=100, table=OF_SELECT_TABLE,
                      dl_dst=port['mac_address'], reg6=TAG_ID,
                      actions='resubmit(,%s)' % OF_NO_SG_VLAN_MATCH),
            mock.call(priority=80,
                      table=OF_NO_SG_VLAN_MATCH,
                      reg6=TAG_ID,
                      actions=('set_field:0->reg%d,'
                               'set_field:0->reg%d,'
                               'mod_vlan_vid:%s,'
                               'resubmit(,%d),resubmit(,%d)') % (
                                   ovsfw_consts.REG_PORT,
                                   ovsfw_consts.REG_NET,
                                   port['vinfo']['tag'],
                                   OF_ACCEPT_OR_INGRESS_TABLE,
                                   FLOW_LOG_CLASSIFY_TABLE)),
            mock.call(priority=100, table=OF_SELECT_TABLE,
                      dl_dst=port['mac_address'], dl_vlan=TAG_ID,
                      actions='resubmit(,%s)' % OF_NO_SG_VLAN_MATCH),
            mock.call(proto=self._write_proto(IPv4, 'udp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_EGRESS_TABLE,
                      priority=120, table=OF_SELECT_TABLE, dl_vlan=TAG_ID,
                      tp_src=68, tp_dst=67, in_port=port['ofport']),
            mock.call(proto=self._write_proto(IPv4, 'udp'),
                      dl_src=port['mac_address'],
                      actions='strip_vlan,resubmit(,%s)' % (
                          OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      priority=100, table=OF_SEC_EXT_TABLE, dl_vlan=TAG_ID,
                      tp_src=68, tp_dst=67, in_port=port['ofport']),
            mock.call(proto=self._write_proto(IPv6, 'udp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_EGRESS_TABLE,
                      priority=120, table=OF_SELECT_TABLE, dl_vlan=TAG_ID,
                      ipv6_dst="ff02::1:2", tp_src=546, tp_dst=547,
                      in_port=port['ofport']),
            mock.call(proto=self._write_proto(IPv6, 'udp'),
                      dl_src=port['mac_address'],
                      actions='strip_vlan,resubmit(,%s)' % (
                          OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      priority=100, table=OF_SEC_EXT_TABLE, dl_vlan=TAG_ID,
                      ipv6_dst="ff02::1:2", tp_src=546, tp_dst=547,
                      in_port=port['ofport']),
            mock.call(priority=200, table=OF_SELECT_TABLE,
                      in_port=port['ofport'], dl_vlan=TAG_ID,
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      dl_src=port['mac_address'],
                      nw_dst='*********/4',
                      nw_src='%s' % FAKE_IP[IPv4],
                      proto=self._write_proto(IPv4, 'igmp'),
                      actions='strip_vlan,normal'),
            mock.call(priority=200, table=OF_SELECT_TABLE,
                      in_port=port['ofport'], dl_vlan=TAG_ID,
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      dl_src=port['mac_address'],
                      ipv6_dst='ff00::/8',
                      ipv6_src='%s' % FAKE_IP[IPv6],
                      proto=self._write_proto(IPv6, 'icmp'),
                      actions='strip_vlan,normal'),
            mock.call(priority=190, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID,
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      nw_dst='*********/4',
                      proto=self._write_proto(IPv4, 'igmp'),
                      actions='normal'),
            mock.call(priority=190, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID,
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      ipv6_dst='ff00::/8',
                      proto=self._write_proto(IPv6, 'icmp'),
                      actions='normal'),
            mock.call(priority=180, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID, reg0=str(TAG_ID),
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      nw_dst='*********/4',
                      proto=self._write_proto(IPv4, 'tcp'),
                      actions='load:1->NXM_NX_REG1[0..11],resubmit(,%s)' %
                              OF_ACCEPT_OR_INGRESS_TABLE),
            mock.call(priority=180, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID, reg0=str(TAG_ID),
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      ipv6_dst='ff00::/8',
                      proto=self._write_proto(IPv6, 'tcp'),
                      actions='load:1->NXM_NX_REG1[0..11],resubmit(,%s)' %
                              OF_ACCEPT_OR_INGRESS_TABLE),
            mock.call(priority=180, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID, reg0=str(TAG_ID),
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      nw_dst='*********/4',
                      proto=self._write_proto(IPv4, 'udp'),
                      actions='load:1->NXM_NX_REG1[0..11],resubmit(,%s)' %
                              OF_ACCEPT_OR_INGRESS_TABLE),
            mock.call(priority=180, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID, reg0=str(TAG_ID),
                      dl_dst='01:00:5e:00:00:00/01:00:5e:00:00:00',
                      ipv6_dst='ff00::/8',
                      proto=self._write_proto(IPv6, 'udp'),
                      actions='load:1->NXM_NX_REG1[0..11],resubmit(,%s)' %
                              OF_ACCEPT_OR_INGRESS_TABLE),
            mock.call(priority=50, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID, actions='drop',
                      proto=self._write_proto(IPv4, 'ip')),
            mock.call(priority=50, table=OF_SELECT_TABLE,
                      dl_vlan=TAG_ID, actions='drop',
                      proto=self._write_proto(IPv6, 'ip')),
            mock.call(actions='drop', in_port=port['ofport'], priority=2010,
                      proto=self._write_proto(IPv4, 'udp'),
                      table=OF_EGRESS_TABLE, udp_dst=68,
                      udp_src=67, dl_vlan=TAG_ID),
            mock.call(actions='drop', in_port=port['ofport'], priority=2010,
                      proto=self._write_proto(IPv6, 'udp'),
                      table=OF_EGRESS_TABLE, udp_dst=546,
                      udp_src=547, dl_vlan=TAG_ID),
            mock.call(actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      dl_src=port['mac_address'], in_port=port['ofport'],
                      priority=2020,
                      proto=self._write_proto(IPv4, 'udp'),
                      table=OF_EGRESS_TABLE,
                      udp_dst=67, udp_src=68, dl_vlan=TAG_ID),
            mock.call(actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      dl_src=port['mac_address'], in_port=port['ofport'],
                      priority=2020,
                      proto=self._write_proto(IPv6, 'udp'),
                      table=OF_EGRESS_TABLE,
                      udp_dst=547, udp_src=546, dl_vlan=TAG_ID),
            mock.call(icmp_type=9,
                      proto=self._write_proto(IPv4, 'icmp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      priority=2020,
                      table=OF_EGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(icmp_type=10,
                      proto=self._write_proto(IPv4, 'icmp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      priority=2020,
                      table=OF_EGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(icmpv6_type=130,
                      proto=self._write_proto(IPv6, 'icmp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      priority=2020, table=OF_EGRESS_TABLE, dl_vlan=TAG_ID,
                      in_port=port['ofport']),
            mock.call(icmpv6_type=131,
                      proto=self._write_proto(IPv6, 'icmp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      priority=2020, table=OF_EGRESS_TABLE, dl_vlan=TAG_ID,
                      in_port=port['ofport']),
            mock.call(icmpv6_type=132,
                      proto=self._write_proto(IPv6, 'icmp'),
                      dl_src=port['mac_address'],
                      actions='resubmit(,%s)' % OF_ACCEPT_OR_INGRESS_TABLE,
                      priority=2020, table=OF_EGRESS_TABLE, dl_vlan=TAG_ID,
                      in_port=port['ofport']),
            mock.call(priority=10, table=OF_ACCEPT_OR_INGRESS_TABLE,
                      dl_vlan=TAG_ID,
                      actions='resubmit(,%s)' % OF_SEC_EXT_TABLE),
            mock.call(priority=100, table=OF_SEC_EXT_TABLE,
                      dl_dst=port['mac_address'], dl_vlan=TAG_ID,
                      actions='drop'),
            mock.call(priority=50, table=OF_SEC_EXT_TABLE,
                      dl_vlan=TAG_ID,
                      actions='resubmit(,%s)' % (
                          ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE)
                      ),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      proto=self._write_proto(IPv4, 'udp'),
                      priority=2020, udp_src=67, udp_dst=68,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID,
                      dl_dst=port['mac_address']),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      proto=self._write_proto(IPv6, 'udp'),
                      priority=2020, udp_src=547, udp_dst=546,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID,
                      dl_dst=port['mac_address']),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      icmp_type=9,
                      proto=self._write_proto(IPv4, 'icmp'),
                      dl_dst=port['mac_address'], priority=2020,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      icmp_type=10,
                      proto=self._write_proto(IPv4, 'icmp'),
                      dl_dst=port['mac_address'], priority=2020,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      icmpv6_type=130,
                      proto=self._write_proto(IPv6, 'icmp'),
                      dl_dst=port['mac_address'], priority=2020,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      icmpv6_type=131,
                      proto=self._write_proto(IPv6, 'icmp'),
                      dl_dst=port['mac_address'], priority=2020,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(actions='strip_vlan,output:%s' % port['ofport'],
                      icmpv6_type=132,
                      proto=self._write_proto(IPv6, 'icmp'),
                      dl_dst=port['mac_address'], priority=2020,
                      table=OF_ACCEPT_OR_INGRESS_TABLE, dl_vlan=TAG_ID),
            mock.call(priority=100, table=OF_SEC_EXT_TABLE,
                      dl_vlan=TAG_ID,
                      reg1='1', actions='drop'),
        ]

        if explicitly_egress_direct:
            table = ovs_constants.ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE
            calls_add_flows += [
                mock.call(actions='output:%s' % DEVICE_OF_PORT,
                          dl_dst=port['mac_address'],
                          priority=12, reg6=TAG_ID,
                          table=table),
                mock.call(actions='set_field:%s->reg9' % DEVICE_OF_PORT,
                          dl_dst=port['mac_address'],
                          dl_vlan=TAG_ID, priority=13, table=table),
                mock.call(actions='mod_vlan_vid:%s,output:%s' % (
                              TAG_ID, PATCH_OF_PORT),
                          dl_dst='00:00:00:00:00:00/01:00:00:00:00:00',
                          dl_src=port['mac_address'], priority=10,
                          reg6=1, table=table),
                mock.call(actions='output:%s' % PATCH_OF_PORT,
                          dl_dst='00:00:00:00:00:00/01:00:00:00:00:00',
                          dl_src=port['mac_address'],
                          dl_vlan=1, priority=10, table=table)
            ]

        if allowed_address_pairs:
            calls_add_flows += [
                mock.call(priority=stateless_firewall.OF_SEL_EGRESS_ALLOW_PRIO,
                          table=OF_SELECT_TABLE,
                          in_port=port['ofport'],
                          proto=self._write_proto(IPv4, 'ip'),
                          dl_vlan=TAG_ID,
                          dl_src='fa:16:3e:72:05:73',
                          nw_src='***************',
                          actions="resubmit(,%s),resubmit(,%s)" %
                                  (OF_EGRESS_TABLE,
                                   FLOW_LOG_CLASSIFY_TABLE)),
                mock.call(priority=stateless_firewall.OF_SEL_EGRESS_ALLOW_PRIO,
                          table=OF_SELECT_TABLE,
                          in_port=port['ofport'],
                          proto=self._write_proto(IPv4, 'ip'),
                          dl_vlan=TAG_ID,
                          dl_src='fa:16:3e:72:05:73',
                          nw_src='***************',
                          actions="resubmit(,%s),resubmit(,%s)" %
                                  (OF_EGRESS_TABLE,
                                   FLOW_LOG_CLASSIFY_TABLE)),
                mock.call(priority=stateless_firewall.OF_SEL_EGRESS_ALLOW_PRIO,
                          table=OF_SELECT_TABLE,
                          in_port=port['ofport'],
                          proto=self._write_proto(IPv6, 'ip'),
                          dl_vlan=TAG_ID,
                          dl_src='fa:16:3e:72:05:73',
                          ipv6_src='fda7:a5cc:3460:1::f:f',
                          actions="resubmit(,%s),resubmit(,%s)" %
                                  (OF_EGRESS_TABLE,
                                   FLOW_LOG_CLASSIFY_TABLE)),
                mock.call(priority=stateless_firewall.OF_SEL_EGRESS_ALLOW_PRIO,
                          table=OF_SELECT_TABLE,
                          in_port=port['ofport'],
                          proto=self._write_proto(IPv6, 'ip'),
                          dl_vlan=TAG_ID,
                          dl_src='fa:16:3e:ee:ee:ee',
                          ipv6_src='fda7:a5cc:3460:1::e:e',
                          actions="resubmit(,%s),resubmit(,%s)" %
                                  (OF_EGRESS_TABLE,
                                   FLOW_LOG_CLASSIFY_TABLE)),
                mock.call(priority=stateless_firewall.OF_SEL_EGRESS_ALLOW_PRIO,
                          table=OF_SELECT_TABLE,
                          in_port=port['ofport'],
                          proto=self._write_proto(IPv4, 'ip'),
                          dl_vlan=TAG_ID,
                          dl_src='fa:16:3e:ff:ff:ff',
                          nw_src='***************',
                          actions="resubmit(,%s),resubmit(,%s)" %
                                  (OF_EGRESS_TABLE,
                                   FLOW_LOG_CLASSIFY_TABLE)),
            ]

        self.mock_add_flow.assert_has_calls(calls_add_flows, any_order=True)

    def _test_rules(self, rule_list, fake_sgid, flow_call_list,
                    any_order=False):
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')
        vif_port = self._fake_vifport(port)
        self.firewall.update_security_group_rules(fake_sgid, rule_list)
        self.firewall._add_rules_flows(self.fake_port_1, vif_port)
        self.mock_add_flow.assert_has_calls(flow_call_list,
                                            any_order=any_order)

    def test_filter_ipv4_ingress(self):
        rule = {'ethertype': IPv4,
                'direction': 'ingress'}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority),
                          dl_dst=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_dst=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'ip'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority),
                      dl_dst=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=IP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_ACCEPT_OR_INGRESS_TABLE))

        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress(self):
        rule = {'ethertype': IPv6,
                'direction': 'ingress'}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority),
                          dl_dst=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_dst=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'ipv6'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority),
                      dl_dst=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=IP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_prefix(self):
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'source_ip_prefix': prefix}
        flow_call_list = []

        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority),
                          dl_dst=self.fake_port_1['mac_address'],
                          dl_vlan=TAG_ID,
                          nw_src=prefix,
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_dst=self.fake_port_1['mac_address'],
                      dl_vlan=TAG_ID,
                      nw_src=prefix,
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'ip'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                nw_src=prefix,
                priority=IP_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_prefix(self):
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'source_ip_prefix': prefix}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority),
                          dl_dst=self.fake_port_1['mac_address'],
                          dl_vlan=TAG_ID,
                          ipv6_src=prefix,
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_ingress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_dst=self.fake_port_1['mac_address'],
                      dl_vlan=TAG_ID,
                      ipv6_src=prefix,
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_ACCEPT_OR_INGRESS_TABLE))
        proto = 'ipv6'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                ipv6_src=prefix,
                priority=IP_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_tcp(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_tcp(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_tcp_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            nw_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_tcp_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            ipv6_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_icmp(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport'],
                icmp_type=icmp_type, icmp_code=icmp_code),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            nw_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_icmp(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport'],
                icmp_type=icmp_type, icmp_code=icmp_code),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            ipv6_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_icmp_prefix(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport'],
                icmp_type=icmp_type, icmp_code=icmp_code),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            nw_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_icmp_prefix(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport'],
                icmp_type=icmp_type, icmp_code=icmp_code),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            ipv6_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_tcp_port(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            tcp_dst=rule['port_range_min'],
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_tcp_port_mac_match_all(self):
        cfg.CONF.set_override("enable_learn_mac_match_all", True, "AGENT")
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            tcp_dst=rule['port_range_min'],
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_tcp_port(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            tcp_dst=rule['port_range_min'],
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_tcp_port_mac_match_all(self):
        cfg.CONF.set_override("enable_learn_mac_match_all", True, "AGENT")
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            tcp_dst=rule['port_range_min'],
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_tcp_mport(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                tcp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_ingress_tcp_mport(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                tcp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_ingress_tcp_mport_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': 'tcp',
                'port_range_min': 10,
                'port_range_max': 100,
                'source_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                nw_src=prefix,
                tcp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_ingress_tcp_mport_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': 'tcp',
                'port_range_min': 10,
                'port_range_max': 100,
                'source_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                ipv6_src=prefix,
                tcp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_ingress_udp(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_udp(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_udp_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            nw_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_udp_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            ipv6_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_udp_port(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': 'udp',
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            udp_dst=10,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_udp_port(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': 'udp',
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            udp_dst=10,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_udp_mport(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': 'udp',
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                udp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_udp_mport(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': 'udp',
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                udp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_ingress_udp_mport_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': 'udp',
                'port_range_min': 10,
                'port_range_max': 100,
                'source_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                nw_src=prefix,
                udp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_ingress_udp_mport_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': 'udp',
                'port_range_min': 10,
                'port_range_max': 100,
                'source_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                ipv6_src=prefix,
                udp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_egress(self):
        rule = {'ethertype': IPv4,
                'direction': 'egress'}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_EGRESS_TABLE))
        proto = 'ip'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=IP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_EGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress(self):
        rule = {'ethertype': IPv6,
                'direction': 'egress'}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_EGRESS_TABLE))
        proto = 'ipv6'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      priority=IP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_EGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_prefix(self):
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          nw_dst=prefix,
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      nw_dst=prefix,
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_EGRESS_TABLE))
        proto = 'ip'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      nw_dst=prefix,
                      priority=IP_PRI,
                      proto=self._write_proto(IPv4, proto),
                      table=OF_EGRESS_TABLE))

        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_prefix(self):
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for proto in ['tcp', 'udp']:
            priority = PROTOCOLS_DEFAULT_PRIO[proto]
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          ipv6_dst=prefix,
                          priority=TCP_UDP_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE))
        proto = 'icmp'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 2),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      ipv6_dst=prefix,
                      priority=ICMP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_EGRESS_TABLE))
        proto = 'ipv6'
        priority = PROTOCOLS_DEFAULT_PRIO[proto]
        flow_call_list.append(
            mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority),
                      dl_src=self.fake_port_1['mac_address'],
                      dl_vlan=self.fake_port_1['vinfo']['tag'],
                      ipv6_dst=prefix,
                      priority=IP_PRI,
                      proto=self._write_proto(IPv6, proto),
                      table=OF_EGRESS_TABLE))

        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_tcp(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_tcp_mac_match_all(self):
        cfg.CONF.set_override("enable_learn_mac_match_all", True, "AGENT")
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_tcp(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_tcp_mac_match_all(self):
        cfg.CONF.set_override("enable_learn_mac_match_all", True, "AGENT")
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_tcp_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'dest_ip_prefix': prefix}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    nw_dst=prefix,
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_tcp_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'dest_ip_prefix': prefix}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    ipv6_dst=prefix,
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_icmp(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3,
                                        icmp_type=icmp_type,
                                        icmp_code=icmp_code),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_icmp(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3,
                                        icmp_type=icmp_type,
                                        icmp_code=icmp_code),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_icmp_prefix(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': 'icmp',
                'dest_ip_prefix': prefix,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3,
                                        icmp_type=icmp_type,
                                        icmp_code=icmp_code),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    nw_dst=prefix,
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_icmp_prefix(self):
        proto = 'icmp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        icmp_type = 10
        icmp_code = 20
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': 'icmp',
                'dest_ip_prefix': prefix,
                'port_range_min': icmp_type,
                'port_range_max': icmp_code}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3,
                                        icmp_type=icmp_type,
                                        icmp_code=icmp_code),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    ipv6_dst=prefix,
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_tcp_port(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE,
                                    tcp_dst=rule['port_range_min'])]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_tcp_port(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE,
                                    tcp_dst=rule['port_range_min'])]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_tcp_mport(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE,
                          tcp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_egress_tcp_mport(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE,
                          tcp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_egress_tcp_mport_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': 'tcp',
                'port_range_min': 10,
                'port_range_max': 100,
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                            rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          nw_dst=prefix,
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE,
                          tcp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_egress_tcp_mport_prefix(self):
        proto = 'tcp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': 'tcp',
                'port_range_min': 10,
                'port_range_max': 100,
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                            rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          ipv6_dst=prefix,
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE,
                          tcp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_egress_udp(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_udp(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_udp_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'dest_ip_prefix': prefix}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    nw_dst=prefix,
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_udp_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_udp_port(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE,
                                    udp_dst=rule['port_range_min'])]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_udp_port(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE,
                                    udp_dst=rule['port_range_min'])]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_udp_mport(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE,
                          udp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_egress_udp_mport(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE,
                          udp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_egress_udp_mport_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100,
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          nw_dst=prefix,
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE,
                          udp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_egress_udp_mport_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100,
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          ipv6_dst=prefix,
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE,
                          udp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    @staticmethod
    def _ip_version_from_address(ip_string):
        ip_net = netaddr.IPNetwork(ip_string)
        if ip_net.version == constants.IP_VERSION_4:
            return IPv4
        if ip_net.version == constants.IP_VERSION_6:
            return IPv6
        raise ValueError('Illegal IP string address')

    def test_get_test_port_ipv6_address(self):
        allowed_address_pairs = [
            {'ip_address': '***************',
             'mac_address': 'fa:16:3e:72:05:73'},
            {'ip_address': '***************',
             'mac_address': 'fa:16:3e:72:05:73'},
            {'ip_address': 'fda7:a5cc:3460:1::f:f',
             'mac_address': 'fa:16:3e:72:05:73'},
            {'ip_address': 'fda7:a5cc:3460:1::e:e',
             'mac_address': 'fa:16:3e:ee:ee:ee'},
            {'ip_address': '***************',
             'mac_address': 'fa:16:3e:ff:ff:ff'}]
        port = self._fake_port(name='tap_vif_port',
                               allowed_address_pairs=allowed_address_pairs)
        vif_port = self._fake_vifport(port)
        nd_target_addresses_use_vif = self._get_test_port_ipv6_address(
                port, vif_port)
        nd_target_addresses_no_vif = self._get_test_port_ipv6_address(port)
        self.assertEqual(sorted(nd_target_addresses_no_vif),
                         sorted(nd_target_addresses_use_vif))

    def test_prepare_port_filter_use_vif(self):
        self._test_prepare_port_filter(use_vir_port=True)

    def test_prepare_port_filter_explicitly_egress_direct_use_vif(self):
        self._test_prepare_port_filter(
            explicitly_egress_direct=True,
            use_vir_port=True)

    def test_prepare_port_filter_with_allowed_address_pairs_use_vif(self):
        self._test_prepare_port_filter(use_vir_port=True,
            allowed_address_pairs=[
                {'ip_address': '***************',
                 'mac_address': 'fa:16:3e:72:05:73'},
                {'ip_address': '***************',
                 'mac_address': 'fa:16:3e:72:05:73'},
                {'ip_address': 'fda7:a5cc:3460:1::f:f',
                 'mac_address': 'fa:16:3e:72:05:73'},
                {'ip_address': 'fda7:a5cc:3460:1::e:e',
                 'mac_address': 'fa:16:3e:ee:ee:ee'},
                {'ip_address': '***************',
                 'mac_address': 'fa:16:3e:ff:ff:ff'}]
        )

    def test_initialize_egress_no_port_security_vlan(self):
        cfg.CONF.set_override("explicitly_egress_direct", True, "AGENT")
        self.firewall.pre_sg_members = {}
        self.firewall._enable_multicast = True
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')
        vif_port = self._fake_vifport(port)
        self.mock_db_get_val.side_effect = [{
            'net_uuid': "e00e6a6a-c88a-4724-80a7-6368a94241d9",
            'network_type': 'vlan',
            'physical_network': 'default',
            'segmentation_id': SEGMENTATION_ID,
            'tag': TAG_ID
        }, TAG_ID, 'interface', {
            "segmentation_id": SEGMENTATION_ID
        }]
        self.firewall._add_flow = mock.Mock()
        self.firewall._initialize_egress_no_port_security(port, vif_port)
        expected_call_args_list = [
            mock.call(actions='resubmit(,%s)' % (
                        OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      in_port=DEVICE_OF_PORT,
                      priority=91,
                      proto='arp',
                      table=OF_ZERO_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst='fa:16:3e:12:34:56',
                      dl_vlan=1,
                      icmpv6_type=133,
                      priority=14,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=94),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst='fa:16:3e:12:34:56',
                      dl_vlan=1,
                      icmpv6_type=134,
                      priority=14,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=94),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst='fa:16:3e:12:34:56',
                      dl_vlan=1,
                      icmpv6_type=135,
                      priority=14,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=94),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst='fa:16:3e:12:34:56',
                      dl_vlan=1,
                      icmpv6_type=136,
                      priority=14,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=94),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst='fa:16:3e:12:34:56',
                      dl_vlan=1,
                      icmpv6_type=137,
                      priority=14,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=94),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=SEGMENTATION_ID,
                      icmpv6_type=134,
                      priority=100,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_ZERO_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=SEGMENTATION_ID,
                      icmpv6_type=136,
                      priority=100,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_ZERO_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=SEGMENTATION_ID,
                      icmpv6_type=137,
                      priority=100,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_ZERO_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=SEGMENTATION_ID,
                      icmpv6_type=133,
                      priority=100,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_ZERO_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_vlan=SEGMENTATION_ID,
                      icmpv6_type=135,
                      nd_target='fe80::1',
                      priority=100,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_ZERO_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_vlan=SEGMENTATION_ID,
                      icmpv6_type=135,
                      nd_target='fe80::f816:3eff:fe12:3456',
                      priority=100,
                      proto='eth_type=0x86dd,ip_proto=58',
                      table=OF_ZERO_TABLE),
            mock.call(actions='resubmit(,56)',
                      in_port=DEVICE_OF_PORT,
                      priority=36,
                      table=OF_ZERO_TABLE),
            mock.call(actions='set_field:1->reg5,set_field:1->reg6,'
                              'resubmit(,65)',
                      in_port=DEVICE_OF_PORT,
                      priority=100,
                      table=OF_TRANSIENT_TABLE),
            mock.call(
                actions='mod_vlan_vid:1,set_field:1->reg5,set_field:1->reg6,'
                'resubmit(,65)',
                in_port=DEVICE_OF_PORT,
                priority=101,
                proto='eth_type=0x0800',
                table=OF_TRANSIENT_TABLE),
            mock.call(actions='resubmit(,65)',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=DEVICE_OF_PORT,
                      nw_dst='********',
                      priority=50,
                      proto='eth_type=0x0800',
                      table=OF_TRANSIENT_TABLE),
            mock.call(actions=('resubmit(,%s)' %
                               OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=DEVICE_OF_PORT,
                      nw_dst='********',
                      priority=50,
                      proto='eth_type=0x0800',
                      table=OF_SEC_EXT_TABLE),
            mock.call(actions='resubmit(,65)',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=DEVICE_OF_PORT,
                      ipv6_dst='fe80::1',
                      priority=50,
                      proto='eth_type=0x86dd',
                      table=OF_TRANSIENT_TABLE),
            mock.call(actions=('resubmit(,%s)' %
                               OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=DEVICE_OF_PORT,
                      ipv6_dst='fe80::1',
                      priority=50,
                      proto='eth_type=0x86dd',
                      table=OF_SEC_EXT_TABLE),
            mock.call(actions='resubmit(,82),resubmit(,150)',
                      priority=80,
                      reg_net=DEVICE_OF_PORT,
                      reg_port=DEVICE_OF_PORT,
                      table=OF_SELECT_TABLE),
            mock.call(actions='resubmit(,82),resubmit(,150)',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=DEVICE_OF_PORT,
                      priority=60,
                      table=OF_SELECT_TABLE),
            mock.call(actions='resubmit(,94)',
                      dl_vlan=DEVICE_OF_PORT,
                      priority=50,
                      table=OF_SEC_EXT_TABLE),
            mock.call(actions='resubmit(,94)',
                      priority=80,
                      reg_port=DEVICE_OF_PORT,
                      table=OF_SEC_EXT_TABLE),
            mock.call(actions='output:1',
                      dl_dst=vif_port.vif_mac,
                      priority=12,
                      reg_net=DEVICE_OF_PORT,
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
            mock.call(actions='set_field:1->reg9',
                      dl_dst=vif_port.vif_mac,
                      dl_vlan=DEVICE_OF_PORT,
                      priority=13,
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
            mock.call(actions='strip_vlan,output:1',
                      dl_dst='fa:16:3e:12:34:56',
                      dl_vlan=1,
                      priority=14,
                      proto='arp',
                      table=OF_ACCEPTED_EGRESS_TRAFFIC_NORMAL_TABLE),
        ]
        self.assertEqual(sorted(expected_call_args_list),
                         sorted(self.firewall._add_flow.call_args_list))

    def test_filter_ipv4_ingress_sctp(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_sctp(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_sctp_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            nw_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_sctp_prefix(self):
        proto = 'udp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'source_ip_prefix': prefix}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            ipv6_src=prefix,
            priority=SPEC_PROTOCOL_PRI,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_sctp_port(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            sctp_dst=10,
            proto=self._write_proto(IPv4, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_sctp_port(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(
            actions=self._learn_ingress_actions(proto, rule['ethertype'],
                priority + 3, ofport=self.fake_port_1['ofport']),
            dl_dst=self.fake_port_1['mac_address'],
            dl_vlan=TAG_ID,
            priority=SPEC_PROTOCOL_PRI,
            sctp_dst=10,
            proto=self._write_proto(IPv6, proto),
            table=OF_ACCEPT_OR_INGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_ingress_sctp_mport(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                sctp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_ingress_sctp_mport(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                sctp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_ingress_sctp_mport_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100,
                'source_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                nw_src=prefix,
                sctp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv4, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_ingress_sctp_mport_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'ingress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100,
                'source_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(mock.call(
                actions=self._learn_ingress_actions(proto, rule['ethertype'],
                    priority + 3, ofport=self.fake_port_1['ofport']),
                dl_dst=self.fake_port_1['mac_address'],
                dl_vlan=TAG_ID,
                ipv6_src=prefix,
                sctp_dst=port,
                priority=SPEC_PROTOCOL_PRI,
                proto=self._write_proto(IPv6, proto),
                table=OF_ACCEPT_OR_INGRESS_TABLE))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_egress_sctp(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_sctp(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_sctp_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'dest_ip_prefix': prefix}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    nw_dst=prefix,
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_sctp_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE)]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_sctp_port(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv4,
                                                            proto),
                                    table=OF_EGRESS_TABLE,
                                    sctp_dst=rule['port_range_min'])]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv6_egress_sctp_port(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 10}
        flow_call_list = [mock.call(actions=self._learn_egress_actions(proto,
                                        rule['ethertype'], priority + 3),
                                    dl_src=self.fake_port_1['mac_address'],
                                    dl_vlan=self.fake_port_1['vinfo']['tag'],
                                    priority=SPEC_PROTOCOL_PRI,
                                    proto=self._write_proto(IPv6,
                                                            proto),
                                    table=OF_EGRESS_TABLE,
                                    sctp_dst=rule['port_range_min'])]
        self._test_rules([rule], FAKE_SGID, flow_call_list)

    def test_filter_ipv4_egress_sctp_mport(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE,
                          sctp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_egress_sctp_mport(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE,
                          sctp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv4_egress_sctp_mport_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv4]
        rule = {'ethertype': IPv4,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100,
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          nw_dst=prefix,
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv4, proto),
                          table=OF_EGRESS_TABLE,
                          sctp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def test_filter_ipv6_egress_sctp_mport_prefix(self):
        proto = 'sctp'
        priority = PROTOCOLS_LEARN_ACTION_PRIO[proto]
        prefix = FAKE_PREFIX[IPv6]
        rule = {'ethertype': IPv6,
                'direction': 'egress',
                'protocol': proto,
                'port_range_min': 10,
                'port_range_max': 100,
                'dest_ip_prefix': prefix}
        flow_call_list = []
        for port in self.mport:
            flow_call_list.append(
                mock.call(actions=self._learn_egress_actions(proto,
                                rule['ethertype'], priority + 3),
                          dl_src=self.fake_port_1['mac_address'],
                          dl_vlan=self.fake_port_1['vinfo']['tag'],
                          ipv6_dst=prefix,
                          priority=SPEC_PROTOCOL_PRI,
                          proto=self._write_proto(IPv6, proto),
                          table=OF_EGRESS_TABLE,
                          sctp_dst=port))
        self._test_rules([rule], FAKE_SGID, flow_call_list, any_order=True)

    def _mock_flow_log_classify_flows(self, port, direction, ethtype, proto,
                                      ofport, port_sec_enabled=True):
        mock_call_list = []

        actions = stateless_firewall.get_flow_log_action(
            self.firewall.learn_cookie, 'ingress', ethtype, proto, 'allow')
        mock_call_list.append(
            mock.call(table=ovs_constants.FLOW_LOG_REENTRY_TABLE,
                      proto=firewall.write_proto(ethtype, proto),
                      priority=2,
                      dl_vlan=port['vinfo']['tag'],
                      reg10=port['vinfo']['tag'],
                      reg11=port['vinfo']['tag'],
                      dl_dst=port['mac_address'],
                      actions=actions))

        if direction == 'ingress':
            actions = stateless_firewall.get_flow_log_action(
                self.firewall.learn_cookie, direction, ethtype, proto,
                ovsfw_consts.ACTION_ACCEPT,
                port_security_enabled=port_sec_enabled)
            mock_call_list.extend([
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=3,
                    dl_vlan=port['vinfo']['tag'],
                    dl_dst=port['mac_address'],
                    actions=actions),
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=3,
                    reg_net=port['vinfo']['tag'],
                    dl_dst=port['mac_address'],
                    actions=actions)
            ])
        else:
            actions = stateless_firewall.get_flow_log_action(
                self.firewall.learn_cookie, direction, ethtype, proto,
                ovsfw_consts.ACTION_ACCEPT,
                strip_vlan=False, local_mac=True,
                port_security_enabled=port_sec_enabled)
            mock_call_list.extend([
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=5,
                    dl_vlan=port['vinfo']['tag'],
                    dl_src=port['mac_address'],
                    actions=actions),
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=5,
                    reg_net=port['vinfo']['tag'],
                    dl_src=port['mac_address'],
                    actions=actions)
            ])

        if direction == 'ingress':
            actions = stateless_firewall.get_flow_log_action(
                self.firewall.learn_cookie, direction, ethtype, proto,
                ovsfw_consts.ACTION_DENY,
                port_security_enabled=port_sec_enabled)
            mock_call_list.extend([
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=22,
                    reg9=ovs_constants.OFPP_NONE,
                    reg12=ovs_constants.REG_DENY_MARK,
                    reg11=port['vinfo']['tag'],
                    dl_vlan=port['vinfo']['tag'],
                    dl_dst=port['mac_address'],
                    actions=actions),
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=22,
                    reg9=ovs_constants.OFPP_NONE,
                    reg12=ovs_constants.REG_DENY_MARK,
                    reg11=port['vinfo']['tag'],
                    reg_net=port['vinfo']['tag'],
                    dl_dst=port['mac_address'],
                    actions=actions)
            ])
        else:
            actions = stateless_firewall.get_flow_log_action(
                self.firewall.learn_cookie, direction, ethtype, proto,
                ovsfw_consts.ACTION_DENY,
                port_security_enabled=port_sec_enabled)
            mock_call_list.extend([
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=22,
                    reg9=ovs_constants.OFPP_NONE,
                    reg12=ovs_constants.REG_DENY_MARK,
                    reg11=port['vinfo']['tag'],
                    dl_vlan=port['vinfo']['tag'],
                    dl_src=port['mac_address'],
                    actions=actions),
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=22,
                    reg9=ovs_constants.OFPP_NONE,
                    reg12=ovs_constants.REG_DENY_MARK,
                    reg11=port['vinfo']['tag'],
                    reg_net=port['vinfo']['tag'],
                    dl_src=port['mac_address'],
                    actions=actions)
            ])

        if ofport is not ovs_lib.INVALID_OFPORT:
            actions = stateless_firewall.get_flow_log_action(
                self.firewall.learn_cookie, direction, ethtype, proto,
                ovsfw_consts.ACTION_ACCEPT, strip_vlan=False,
                port_security_enabled=port_sec_enabled)
            mock_call_list.extend([
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=5,
                    dl_vlan=port['vinfo']['tag'],
                    reg9=ofport,
                    dl_dst=port['mac_address'],
                    actions=actions),
                mock.call(
                    table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                    proto=firewall.write_proto(ethtype, proto),
                    priority=5,
                    reg_net=port['vinfo']['tag'],
                    reg9=ofport,
                    dl_dst=port['mac_address'],
                    actions=actions)])

        return mock_call_list

    def _mock_flow_log_classify_uninstall_flows(self, port):
        mock_call_list = []

        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for eth_type in [IPv4, IPv6]:
                for direction in ['ingress', 'egress']:
                    mock_call_list.append(mock.call(
                        table=ovs_constants.FLOW_LOG_REENTRY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        dl_vlan=port['vinfo']['tag'],
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        dl_dst=port['mac_address']))
                    if direction == 'ingress':
                        mock_call_list.extend([
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                dl_vlan=port['vinfo']['tag'],
                                dl_dst=port['mac_address']),
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                reg_net=port['vinfo']['tag'],
                                dl_dst=port['mac_address'])
                        ])
                    else:
                        mock_call_list.extend([
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                dl_vlan=port['vinfo']['tag'],
                                dl_src=port['mac_address']),
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                reg_net=port['vinfo']['tag'],
                                dl_src=port['mac_address'])
                        ])
                    if port['ofport'] is not ovs_lib.INVALID_OFPORT:
                        mock_call_list.extend([
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                dl_vlan=port['vinfo']['tag'],
                                reg9=port['ofport'],
                                dl_dst=port['mac_address']),
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                reg_net=port['vinfo']['tag'],
                                reg9=port['ofport'],
                                dl_dst=port['mac_address'])
                        ])

                for fixed_ip in port['fixed_ips']:
                    if self._ip_version_from_address(fixed_ip) == IPv4:
                        mock_call_list.append(mock.call(
                            table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                            proto=firewall.write_proto(IPv4, proto),
                            nw_dst=fixed_ip,
                            dl_dst=port['mac_address']))
                    if self._ip_version_from_address(fixed_ip) == IPv6:
                        mock_call_list.append(mock.call(
                            table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                            proto=firewall.write_proto(IPv6, proto),
                            ipv6_dst=fixed_ip,
                            dl_dst=port['mac_address']))

                mock_call_list.extend([
                    mock.call(
                        table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        reg9=ovs_constants.OFPP_NONE,
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        dl_vlan=port['vinfo']['tag'],
                        dl_src=port['mac_address']),
                    mock.call(
                        table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        reg9=ovs_constants.OFPP_NONE,
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        reg_net=port['vinfo']['tag'],
                        dl_src=port['mac_address'])
                ])

        return mock_call_list

    def _mock_init_flow_log_flows(self, port):
        mock_call_list = [
            mock.call(priority=19,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      dl_vlan=port['vinfo']['tag'],
                      reg9=ovs_constants.OFPP_NONE,
                      actions="drop"),
            mock.call(priority=20,
                      table=ovs_constants.FLOW_LOG_REENTRY_TABLE,
                      dl_vlan=port['vinfo']['tag'],
                      reg9=ovs_constants.OFPP_NONE,
                      actions="drop"),
            mock.call(priority=1,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      reg_net=port['vinfo']['tag'],
                      actions="output:NXM_NX_REG9[0..15]"),
            mock.call(priority=1,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      reg10=port['vinfo']['tag'],
                      actions="output:NXM_NX_REG9[0..15]"),
            mock.call(priority=0,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      dl_vlan=port['vinfo']['tag'],
                      actions="resubmit(,{}),resubmit(,{})".format(
                          OF_ACCEPT_OR_INGRESS_TABLE,
                          ovs_constants.FLOW_LOG_REENTRY_TABLE)),
            mock.call(priority=1,
                      table=ovs_constants.FLOW_LOG_REENTRY_TABLE,
                      dl_vlan=port['vinfo']['tag'],
                      dl_dst=port['mac_address'],
                      actions="strip_vlan,output:NXM_NX_REG9[0..15]"),
            mock.call(
                priority=3,
                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                reg10=port['vinfo']['tag'],
                reg9=port['ofport'],
                actions=("mod_vlan_vid:%s,output:NXM_NX_REG9[0..15]" %
                         port['vinfo']['tag'])),
            mock.call(priority=2,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      reg10=port['vinfo']['tag'],
                      dl_vlan=port['vinfo']['tag'],
                      actions="strip_vlan,output:NXM_NX_REG9[0..15]"),
            mock.call(priority=1,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      reg10=port['vinfo']['tag'],
                      actions="output:NXM_NX_REG9[0..15]"),
            mock.call(table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      priority=4,
                      dl_vlan=port['vinfo']['tag'],
                      dl_src=port['mac_address'],
                      actions="{},resubmit(,{}),resubmit(,{})".format(
                          "move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG10[0..11],"
                          "move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG11[0..11]",
                          OF_ACCEPT_OR_INGRESS_TABLE,
                          ovs_constants.FLOW_LOG_REENTRY_TABLE))
        ]

        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for eth_type in [IPv4, IPv6]:
                actions = self.firewall._get_deny_action(port)
                mock_call_list.append(
                    mock.call(priority=1,
                              table=OF_EGRESS_TABLE,
                              proto=firewall.write_proto(eth_type, proto),
                              dl_vlan=port['vinfo']['tag'],
                              actions=actions))

        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for eth_type in [IPv4, IPv6]:
                for direction in ['ingress', 'egress']:
                    mock_call_list.extend(
                        self._mock_flow_log_classify_flows(
                            port, direction, eth_type, proto, port['ofport']))

        return mock_call_list

    def test__init_flow_log(self):
        cfg.CONF.set_override("explicitly_egress_direct", True, "AGENT")
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')
        self.mock_db_get_val.side_effect = [{
            'net_uuid': "e00e6a6a-c88a-4724-80a7-6368a94241d9",
            'network_type': 'vlan',
            'physical_network': 'default',
            'segmentation_id': SEGMENTATION_ID,
            'tag': TAG_ID
        }, TAG_ID, 'interface', {
            "segmentation_id": SEGMENTATION_ID
        }]
        self.firewall._add_flow = mock.Mock()
        self.firewall._get_patch_ofport = mock.Mock(
            return_value=port['ofport'])
        self.firewall._port_flow_log_enabled = mock.Mock(return_value=True)

        self.firewall._init_flow_log(port)
        expected_call_args_list = self._mock_init_flow_log_flows(port)
        self.assertEqual(sorted(expected_call_args_list),
                         sorted(self.firewall._add_flow.call_args_list))

    def _mock_init_flow_log_no_sg(self, port):
        mock_call_list = []
        mock_call_list.extend([
            mock.call(priority=1,
                      table=ovs_constants.FLOW_LOG_REENTRY_TABLE,
                      dl_vlan=port['vinfo']['tag'],
                      dl_dst=port['mac_address'],
                      actions="strip_vlan,output:NXM_NX_REG9[0..15]"),
            mock.call(priority=2,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      reg6=port['vinfo']['tag'],
                      dl_vlan=port['vinfo']['tag'],
                      dl_dst=port['mac_address'],
                      actions="strip_vlan,output:NXM_NX_REG9[0..15]"),
            mock.call(priority=0,
                      table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                      dl_vlan=port['vinfo']['tag'],
                      actions="resubmit(,{}),resubmit(,{})".format(
                          OF_ACCEPT_OR_INGRESS_TABLE,
                          ovs_constants.FLOW_LOG_REENTRY_TABLE))
        ])

        for fixed_ip in port['fixed_ips']:
            for proto in [constants.PROTO_NAME_TCP,
                          constants.PROTO_NAME_UDP,
                          constants.PROTO_NAME_ICMP]:
                for eth_type in [IPv4, IPv6]:
                    actions = stateless_firewall.get_flow_log_action(
                        self.firewall.learn_cookie, 'ingress', eth_type,
                        proto, ovsfw_consts.ACTION_ACCEPT,
                        port_security_enabled=False)
                    ip_version = self._ip_version_from_address(fixed_ip)
                    if eth_type == IPv4 and ip_version == IPv4:
                        mock_call_list.append(mock.call(
                            table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                            proto=firewall.write_proto(IPv4, proto),
                            priority=3,
                            nw_dst=fixed_ip,
                            dl_dst=port['mac_address'],
                            actions=actions))
                    if eth_type == IPv6 and ip_version == IPv6:
                        mock_call_list.append(mock.call(
                            table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                            proto=firewall.write_proto(IPv6, proto),
                            priority=3,
                            ipv6_dst=fixed_ip,
                            dl_dst=port['mac_address'],
                            actions=actions))

        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for eth_type in [IPv4, IPv6]:
                for direction in ['ingress', 'egress']:
                    mock_call_list.extend(
                        self._mock_flow_log_classify_flows(
                            port, direction, eth_type, proto,
                            port['ofport'], False))

                actions = stateless_firewall.get_flow_log_action(
                    self.firewall.learn_cookie, 'egress', eth_type, proto,
                    ovsfw_consts.ACTION_ACCEPT,
                    strip_vlan=False, local_mac=True,
                    port_security_enabled=False)
                mock_call_list.extend([
                    mock.call(
                        table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        reg9=ovs_constants.OFPP_NONE,
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        priority=20,
                        dl_vlan=port['vinfo']['tag'],
                        dl_src=port['mac_address'],
                        actions=actions),
                    mock.call(
                        table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        reg9=ovs_constants.OFPP_NONE,
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        priority=20,
                        reg_net=port['vinfo']['tag'],
                        dl_src=port['mac_address'],
                        actions=actions)])

        return mock_call_list

    def test__init_flow_log_for_port_no_security(self):
        cfg.CONF.set_override("explicitly_egress_direct", True, "AGENT")
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')
        self.mock_db_get_val.side_effect = [{
            'net_uuid': "e00e6a6a-c88a-4724-80a7-6368a94241d9",
            'network_type': 'vlan',
            'physical_network': 'default',
            'segmentation_id': SEGMENTATION_ID,
            'tag': TAG_ID
        }, TAG_ID, 'interface', {
            "segmentation_id": SEGMENTATION_ID
        }]
        self.firewall._add_flow = mock.Mock()
        self.firewall._del_flows = mock.Mock()
        self.firewall._get_patch_ofport = mock.Mock(
            return_value=port['ofport'])
        self.firewall._port_flow_log_enabled = mock.Mock(return_value=True)
        self.firewall._init_flow_log_for_port_no_security(port)

        expected_call_args_list = (
            self._mock_flow_log_classify_uninstall_flows(port))
        self.assertEqual(sorted(expected_call_args_list),
                         sorted(self.firewall._del_flows.call_args_list))

        expected_call_args_list = self._mock_init_flow_log_no_sg(port)
        self.assertEqual(sorted(expected_call_args_list),
                         sorted(self.firewall._add_flow.call_args_list))

    def test_update_port_flow_log(self):
        cfg.CONF.set_override("explicitly_egress_direct", True, "AGENT")
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')

        self.firewall._remove_flow_log_flows = mock.Mock()
        self.firewall._init_flow_log = mock.Mock()
        self.firewall._block_flow_and_install_flow_log_drop_learn = mock.Mock()

        self.firewall.update_port_flow_log(port)
        self.firewall._remove_flow_log_flows.assert_called_with(port)
        self.firewall._init_flow_log.assert_called_with(port)
        call_list = [mock.call(port, 71, 51),
                     mock.call(port, 82, 101)]
        (self.firewall._block_flow_and_install_flow_log_drop_learn.
         assert_has_calls(call_list, any_order=True))

    def test__get_deny_action(self):
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')
        with mock.patch.object(cfg.CONF, "FLOW_LOG") as flow_log:
            flow_log.enable_flow_log = mock.Mock(return_value=True)

            port['vinfo']['flow_log_enabled'] = False
            actions = self.firewall._get_deny_action(port)
            expected = ("move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG10[0..11],"
                        "move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG11[0..11],"
                        "set_field:65535->reg9")
            self.assertEqual(actions, expected)

            port['vinfo']['flow_log_enabled'] = True
            actions = self.firewall._get_deny_action(port)
            expected = ("move:NXM_OF_VLAN_TCI[0..11]->NXM_NX_REG11[0..11],"
                        "set_field:65535->reg9,set_field:4095->reg12,"
                        "resubmit(,150),output:NXM_NX_REG9[0..15]")
            self.assertEqual(actions, expected)

    def test__remove_flow_log_flows(self):
        port = self._fake_port(name='fake_dev_1',
                               device_owner='compute:nova')

        self.firewall._del_flows = mock.Mock()
        self.firewall._get_patch_ofport = mock.Mock(
            return_value=port['ofport'])

        self.firewall._remove_flow_log_flows(port)
        mock_call_list = []
        for proto in [constants.PROTO_NAME_TCP,
                      constants.PROTO_NAME_UDP,
                      constants.PROTO_NAME_ICMP]:
            for eth_type in [IPv4, IPv6]:
                for direction in ['ingress', 'egress']:
                    mock_call_list.append(mock.call(
                        table=ovs_constants.FLOW_LOG_REENTRY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        dl_vlan=port['vinfo']['tag'],
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        dl_dst=port['mac_address']))
                    if direction == 'ingress':
                        mock_call_list.extend([
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                dl_vlan=port['vinfo']['tag'],
                                dl_dst=port['mac_address']),
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                reg_net=port['vinfo']['tag'],
                                dl_dst=port['mac_address'])
                        ])
                    else:
                        mock_call_list.extend([
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                dl_vlan=port['vinfo']['tag'],
                                dl_src=port['mac_address']),
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                reg_net=port['vinfo']['tag'],
                                dl_src=port['mac_address'])
                        ])
                    if port['ofport'] is not ovs_lib.INVALID_OFPORT:
                        mock_call_list.extend([
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                dl_vlan=port['vinfo']['tag'],
                                reg9=port['ofport'],
                                dl_dst=port['mac_address']),
                            mock.call(
                                table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                                proto=firewall.write_proto(eth_type, proto),
                                reg_net=port['vinfo']['tag'],
                                reg9=port['ofport'],
                                dl_dst=port['mac_address'])
                        ])

                mock_call_list.extend([
                    mock.call(
                        table=OF_SELECT_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        dl_vlan=port['vinfo']['tag'],
                        dl_dst=port['mac_address']),
                    mock.call(
                        table=OF_SEC_EXT_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        dl_vlan=port['vinfo']['tag'],
                        dl_dst=port['mac_address']),
                    mock.call(
                        table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        reg9=ovs_constants.OFPP_NONE,
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        dl_vlan=port['vinfo']['tag'],
                        dl_src=port['mac_address']),
                    mock.call(
                        table=ovs_constants.FLOW_LOG_CLASSIFY_TABLE,
                        proto=firewall.write_proto(eth_type, proto),
                        reg9=ovs_constants.OFPP_NONE,
                        reg10=port['vinfo']['tag'],
                        reg11=port['vinfo']['tag'],
                        reg_net=port['vinfo']['tag'],
                        dl_src=port['mac_address'])
                ])
        self.assertEqual(sorted(mock_call_list),
                         sorted(self.firewall._del_flows.call_args_list))

    def test_process_resubmit_dscp_table(self):
        port = {'cloud_attributes': {'cloud_attrs': {'dscp_learn': True}}}
        # Test get dscp resubmit action
        with mock.patch.object(cfg.CONF.AGENT, 'enable_lbaas_dscp', True):
            actual = self.firewall._process_resubmit_dscp_table(port)
            expected = ',resubmit(,%s),' % ovs_constants.DSCP_TABLE
            self.assertEqual(expected, actual)

        # Test to process NoneType value
        with mock.patch.object(cfg.CONF.AGENT, 'enable_lbaas_dscp', True):
            actual = self.firewall._process_resubmit_dscp_table(None)
            self.assertEqual('', actual)
