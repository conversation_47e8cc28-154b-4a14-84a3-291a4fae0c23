#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr

from oslo_versionedobjects import fields as obj_fields

from neutron.common import utils
from neutron.db.models import route_table as rt
from neutron.objects import base
from neutron.objects import common_types


@base.NeutronObjectRegistry.register
class RouteTable(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTable

    fields = {
        'id': common_types.UUIDField(),
        'project_id': obj_fields.StringField(nullable=True),
        'name': obj_fields.StringField(nullable=True),
        'router_id': common_types.UUIDField(),
        'table_id': obj_fields.IntegerField(),
        'routes': obj_fields.ListOfObjectsField(
            'RouteTableRoutes', nullable=True),
        'subnets': obj_fields.ListOfObjectsField(
            'RouteTableSubnetBindings', nullable=True),
        'is_default': obj_fields.BooleanField(default=False),
    }

    fields_no_update = ['project_id', 'is_default']
    synthetic_fields = ['routes', 'subnets', 'is_default']
    extra_filter_names = {'is_default'}

    def from_db_object(self, db_obj):
        super(RouteTable, self).from_db_object(db_obj)
        if self._load_synthetic_fields:
            setattr(self, 'is_default',
                    bool(db_obj.get('default_routetable')))
            self.obj_reset_changes(['is_default'])

    def create(self):
        is_default = self.is_default
        with self.db_context_writer(self.obj_context):
            super(RouteTable, self).create()
            if is_default:
                default_route = DefaultRouteTable(
                    self.obj_context,
                    router_id=self.router_id,
                    routetable_id=self.id)
                default_route.create()
                self.is_default = True
                self.obj_reset_changes(['is_default'])


@base.NeutronObjectRegistry.register
class DefaultRouteTable(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.DefaultRouteTable

    fields = {
        'router_id': common_types.UUIDField(),
        'routetable_id': common_types.UUIDField(),
    }

    fields_no_update = ['routetable_id']
    primary_keys = ['router_id']


@base.NeutronObjectRegistry.register
class RouteTableRoutes(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTableRoutes

    fields = {
        'routetable_id': common_types.UUIDField(),
        'destination': common_types.IPNetworkField(),
        'nexthop': obj_fields.IPAddressField(),
        'type': obj_fields.StringField()
    }

    primary_keys = ['routetable_id', 'destination']
    foreign_keys = {'RouteTable': {'routetable_id': 'id'}}

    @classmethod
    def modify_fields_from_db(cls, db_obj):
        result = super(RouteTableRoutes, cls).modify_fields_from_db(db_obj)
        if 'destination' in result:
            result['destination'] = utils.AuthenticIPNetwork(
                result['destination'])
        if 'nexthop' in result:
            result['nexthop'] = netaddr.IPAddress(result['nexthop'])
        return result

    @classmethod
    def modify_fields_to_db(cls, fields):
        result = super(RouteTableRoutes, cls).modify_fields_to_db(fields)
        if 'destination' in result:
            result['destination'] = cls.filter_to_str(result['destination'])
        if 'nexthop' in result:
            result['nexthop'] = cls.filter_to_str(result['nexthop'])
        return result


@base.NeutronObjectRegistry.register
class RouteTableSubnetBindings(base.NeutronDbObject):

    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTableSubnetBindings

    fields = {
        'routetable_id': common_types.UUIDField(),
        'subnet_id': common_types.UUIDField(),
    }

    primary_keys = ['subnet_id', 'routetable_id']
    foreign_keys = {'RouteTable': {'routetable_id': 'id'}}
