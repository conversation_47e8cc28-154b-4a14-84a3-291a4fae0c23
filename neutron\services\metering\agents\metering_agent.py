# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import copy
import os
import socket
import sys
import time

import eventlet
from neutron_lib.agent import topics
from neutron_lib import constants
from neutron_lib import context
from neutron_lib.utils import runtime
from oslo_config import cfg
from oslo_log import log as logging
import oslo_messaging
from oslo_serialization import jsonutils
from oslo_service import loopingcall
from oslo_service import periodic_task
from oslo_service import service
from oslo_utils import fileutils
from oslo_utils import timeutils
from pykafka import exceptions as kafka_exc

from neutron._i18n import _
from neutron.agent import rpc as agent_rpc
from neutron.common import config as common_config
from neutron.common import constants as n_const
from neutron.common import rpc as n_rpc
from neutron.conf.agent import common as config
from neutron.conf.services import metering_agent
from neutron import manager
from neutron import service as neutron_service
from neutron.services.metering.agents import kafka_manager as kafka_mgt
from neutron.services.metering.agents import ProcessManager as pm
from neutron.services.metering.collection import collect_dhcp
from neutron.services.metering.collection import collect_router
from neutron.services.metering.collection import collect_vpc
from neutron.services.metering.common import log_derorator
from neutron.services.metering.drivers import utils as driverutils

LOG = logging.getLogger(__name__)

ROUTER_COUNTER_LOG = 'router_counter.log'
EIP_COUNTER_LOG = 'eip_counter.log'
DHCPVM_COUNTER_LOG = 'vm_counter.log'
VPC_TO_VPC_LOG = 'vpc_to_vpc.log'

router_cnt_log = log_derorator.MonitorLogger(ROUTER_COUNTER_LOG)
eip_cnt_log = log_derorator.MonitorLogger(EIP_COUNTER_LOG)
dhcpvm_cnt_log = log_derorator.MonitorLogger(DHCPVM_COUNTER_LOG)
vpc_to_vpc_log = log_derorator.MonitorLogger(VPC_TO_VPC_LOG)


class MeteringPluginRpc(object):

    def __init__(self, host):
        # NOTE(yamamoto): super.__init__() call here is not only for
        # aesthetics.  Because of multiple inheritances in MeteringAgent,
        # it's actually necessary to initialize parent classes of
        # manager.Manager correctly.
        self.host = host
        super(MeteringPluginRpc, self).__init__(host)
        target = oslo_messaging.Target(topic=topics.METERING_PLUGIN,
                                       version='1.0')
        self.client = n_rpc.get_client(target)
        target_collect = oslo_messaging.Target(
            topic=topics.METERING_COLLECT_PLUGIN,
            version='1.0')
        self.collect_client = n_rpc.get_client(target_collect)

    def get_plugin_metering_router_vpc(self, context, flag='all'):
        try:
            cctxt = self.client.prepare()
            return cctxt.call(context, 'metering_router_vpc_callback',
                              host=self.host, flag=flag)
        except oslo_messaging.MessagingTimeout:
            LOG.warning('Failed get metering routers vpcs from neutron-db.')
            return {'router': [], 'vpc2vpc': []}
        except Exception:
            LOG.exception("Failed get metering vpcs.")

    def get_sync_data_metering(self, context):
        try:
            cctxt = self.client.prepare()
            return cctxt.call(context, 'get_sync_data_metering',
                              host=self.host)
        except oslo_messaging.MessagingTimeout:
            LOG.warning('Failed synchronizing routers from neutron-server')
            return []
        except Exception:
            LOG.exception("Failed synchronizing routers")

    def sync_router_metering(self, context, router):
        try:
            LOG.debug("sync router metering, router:%s.", router)
            cctxt = self.client.prepare()
            return cctxt.cast(context, 'sync_router_metering', router=router)
        except oslo_messaging.MessagingTimeout:
            LOG.warning('Failed synchronizing routers from neutron-server')
            return []
        except Exception as ex:
            LOG.exception("Failed synchronizing routers, raise %s", ex)

    def sync_metering_vpc_routers(self, context, sync_vpcs):
        try:
            cctxt = self.collect_client.prepare()
            return cctxt.cast(context, 'sync_metering_vpc_routers',
                              host=self.host, sync_vpcs=sync_vpcs)
        except oslo_messaging.MessagingTimeout:
            LOG.warning('Failed sync vpc router info from neutron-server')
            return []
        except Exception:
            LOG.exception("Failed sync vpc router info")

    def get_metering_fip(self, context, floatingip_id):
        try:
            cctxt = self.client.prepare()
            return cctxt.call(context, 'get_metering_fip',
                              floatingip_id=floatingip_id)
        except oslo_messaging.MessagingTimeout:
            LOG.warning('Failed get metering fip info from neutron-server')
            return {}
        except Exception:
            LOG.exception("Failed get metering fip info")
            return {}


class MeteringAgent(MeteringPluginRpc, manager.Manager):

    def __init__(self, host, conf=None):

        self.host = host
        self.hostname = socket.gethostname()
        self.conf = conf or cfg.CONF
        self.enable_sdn = cfg.CONF.is_sdn_arch
        self.enable_meter = cfg.CONF.enable_meter
        if not self.enable_sdn:
            self._load_drivers()
        self.context = context.get_admin_context_without_session()
        super(MeteringAgent, self).__init__(host=host)

        measure_interval = self.conf.measure_interval
        vm_interval = self.conf.report_vm_interval
        self.report_router_tcp_con_interval = (
            self.conf.report_router_tcp_con_interval)
        self.report_router_udp_con_interval = (
            self.conf.report_router_udp_con_interval)
        self.report_router_icmp_con_interval = (
            self.conf.report_router_icmp_con_interval)
        self.vpc2vpc_report_interval = self.conf.report_vpc2vpc_interval
        self.sync_router_vpc_interval = self.conf.sync_router_vpc_interval
        self.enable_monitor_router = self.conf.enable_monitor_router
        self.enable_monitor_vpc = self.conf.enable_monitor_vpc
        self.enable_monitor_vpc2vpc = self.conf.enable_monitor_vpc2vpc
        self.enable_monitor_eip = self.conf.enable_monitor_eip
        self.enable_monitor_vm = self.conf.enable_monitor_vm
        self.enable_monitor_router_vpc2vpc = (
            self.conf.enable_monitor_router_vpc2vpc)
        self.metering_router_vpc = {'router': [], 'vpc2vpc': []}

        if not self.enable_sdn:
            try:
                # init metering_router_vpc
                self.metering_router_vpc = (
                    self.get_plugin_metering_router_vpc(
                        self.context, flag='all'))
                # LOG.info('MeteringAgent:init:metering_router_vpc %s',
                #          self.metering_router_vpc)
            except oslo_messaging.MessagingTimeout:
                LOG.error('Sync vpc and router failed in agent init'
                          'from neutron-server')
            except Exception as e:
                LOG.exception('Sync vpc and router failed in agent init'
                              'from neutron-server %s', e)

            self.router_last_report = 0
            self.vpc_last_report = 0
            self.vpc_to_vpc_last_report = 0
            self.router_vpc_last_report = 0

            # Monitor eip
            if self.enable_monitor_eip:
                self.metering_loop = (
                    loopingcall.FixedIntervalLoopingCall(
                        self._metering_loop))
                self.metering_loop.start(
                    interval=measure_interval,
                    stop_on_exception=False)

            # Monitor router
            if self.enable_monitor_router:
                self.monitor_router_counter_loop = (
                    loopingcall.FixedIntervalLoopingCall(
                        self.monitor_router_counter))
                self.monitor_router_counter_loop.start(
                    interval=self.conf.report_interval,
                    stop_on_exception=False)

            # Monitor vpc2vpc
            if self.enable_monitor_vpc2vpc:
                self.metering_vpc_to_vpc_loop = (
                    loopingcall.FixedIntervalLoopingCall(
                        self.metering_vpc_to_vpc))
                self.metering_vpc_to_vpc_loop.start(
                    interval=self.conf.report_interval,
                    stop_on_exception=False)

            # Period sync vpc and router from db
            if self.enable_monitor_router_vpc2vpc:
                self.sync_metering_rv_loop = (
                    loopingcall.FixedIntervalLoopingCall(
                        self.sync_metering_router_vpc2vpc))
                self.sync_metering_rv_loop.start(
                    interval=self.sync_router_vpc_interval,
                    stop_on_exception=False)

            self.routers = {}
            self.metering_infos = {}
            self.label_report_info = {}
            self.collect_router = collect_router.MonitorRouter()
            LOG.info('Reading metering-agent.ini file:\n'
                     ' measure_interval: %s enable_monitor_router:%s '
                     'enable_monitor_vpc: %s enable_monitor_vpc2vpc: %s'
                     ' enable_monitor_eip: %s', measure_interval,
                     self.enable_monitor_router, self.enable_monitor_vpc,
                     self.enable_monitor_vpc2vpc, self.enable_monitor_eip)

        # Collect vm connection status no matter if is_sdn_arch
        if self.enable_monitor_vm:
            self.metering_vm_loop = (
                loopingcall.FixedIntervalLoopingCall(self.metering_vm))
            self.metering_vm_loop.start(
                interval=vm_interval, stop_on_exception=False)

        LOG.info('Reading metering-agent.ini file:\n'
                 'measure_interval: %s vm_interval:%s enable_monitor_vm: %s',
                 measure_interval, vm_interval, self.enable_monitor_vm)

        self.last_report = 0
        self.vm_last_report = 0

        self.initKafa = True
        self.eipKafkaManager = None
        self.routerKafkaManager = None
        self.vmKafkaManager = None
        self.vpc2VpcKafkaManager = None

        self.fip_metering_path = os.path.join(
            self.conf.state_path, "fip_cache")
        if not os.path.exists(self.fip_metering_path):
            os.makedirs(self.fip_metering_path)
            fileutils.ensure_tree(self.fip_metering_path, mode=0o755)
            LOG.info("Create fip metering cache path: %s",
                     self.fip_metering_path)

    def _load_drivers(self):
        """Loads plugin-driver from configuration."""
        LOG.info("Loading Metering driver %s", self.conf.driver)
        if not self.conf.driver:
            raise SystemExit(_('A metering driver must be specified'))
        self.metering_driver = driverutils.load_metering_driver(self,
                                                                self.conf)

    def after_start(self):
        eventlet.spawn_n(self.monitor_resource, context)

    def monitor_resource(self, context):
        pass

    def monitor_router_counter(self):
        try:
            LOG.info('Router loop start, self.RouterKafkaManager=%s ',
                     self.routerKafkaManager)

            ts = timeutils.utcnow_ts()
            delta = ts - self.router_last_report
            LOG.info('Router loop, delta=%s', delta)

            report_interval = self.conf.report_interval
            if delta >= report_interval:
                begin_time = time.time()
                self.routerKafkaManager = kafka_mgt.RouterKafkaManager()
                self.routerKafkaManager.reconnect_kafa()
                tend = time.time()

                LOG.info('Router loop, delta=%s, kafka_dict=%s, escaped=%s',
                         delta, self.routerKafkaManager.kafka_dict,
                         (tend - begin_time))

                router_snapshot = copy.deepcopy(
                    self.metering_router_vpc['router'])
                if router_snapshot:
                    try:
                        self.collect_router.monitor_resource_router(
                            router_snapshot,
                            self.routers,
                            router_cnt_log,
                            self.routerKafkaManager)

                    except kafka_exc.NoBrokersAvailableError:
                        LOG.warning(
                            'Re-init Kafka client in router loop')
                        self.initKafa = True
                    except (kafka_exc.SocketDisconnectedError,
                            kafka_exc.LeaderNotAvailable) as ex:
                        LOG.exception("Kafka connection has lost, "
                                      "reconnect and resending...ex=%s ", ex)
                        self.initKafa = True
                    except kafka_exc.UnknownError as e:
                        self.initKafa = True
                        LOG.exception('Send failed in router loop, ex=%s', e)
                    except Exception as e:
                        LOG.exception('Send failed in router loop, e=%s', e)
                        self.initKafa = True

                LOG.info('Router counter router_counter=%s',
                         self.routerKafkaManager.router_counter)

                escaped_time = time.time() - begin_time
                LOG.info('Router counter loop exit, escaped=%s', escaped_time)
                self.router_last_report = ts
                if escaped_time >= report_interval:
                    LOG.warning("Found handle tcp conn time escape %s",
                                escaped_time)
        except Exception as e:
            LOG.error('Router counter loop failed, e=%s', e)

    def metering_vpc_to_vpc(self):
        try:
            LOG.info(
                'Vpc2vpc counter loop task starting, Vpc2VpcKafkaManager=%s',
                self.vpc2VpcKafkaManager)

            ts = timeutils.utcnow_ts()
            delta = ts - self.vpc_to_vpc_last_report
            if delta >= self.vpc2vpc_report_interval:
                begin_time = time.time()
                self.vpc2VpcKafkaManager = kafka_mgt.Vpc2VpcKafkaManager()
                self.vpc2VpcKafkaManager.reconnect_kafa()
                tend = time.time()
                LOG.info('Vpc2vpc loop, delta=%s, kafka_dict=%s, escaped=%s',
                         delta, self.vpc2VpcKafkaManager.kafka_dict,
                         (tend - begin_time))

                try:
                    # if not exist request from server in advance
                    vpc2vpc_snapshot = copy.deepcopy(
                        self.metering_router_vpc['vpc2vpc'])

                    if not vpc2vpc_snapshot:
                        self.vpc_to_vpc_last_report = ts
                        return

                    self.monitor_metering_vpc(self.context,
                                              vpc2vpc_snapshot)

                    LOG.debug('Agent:period_vpc2vpc_info %s',
                              self.metering_router_vpc['vpc2vpc'])
                except kafka_exc.NoBrokersAvailableError:
                    LOG.warning('Reinit Kafka client in vpc to vpc')
                    self.initKafa = True
                except (kafka_exc.SocketDisconnectedError,
                        kafka_exc.LeaderNotAvailable) as ex:
                    LOG.exception("Kafka connection has lost, "
                                  "Reconnect in vpc2vpc loop, ex=%s ", ex)
                    self.initKafa = True

                self.vpc_to_vpc_last_report = ts

                escaped_time = time.time() - begin_time
                if escaped_time > self.vpc2vpc_report_interval:
                    LOG.warning('Vpc2vpc loop escape %s ', escaped_time)
        except Exception as e:
            LOG.error('Router counter loop failed, e=%s', e)

    def _sync_metering_vpc_routers(self, context, ns_routers, metering_vpcs):
        # Sync local router namespaces and routers in metering_vpcs in db
        # All routers info of metering_vpcs on this node node in the database.
        db_routers = []
        # All metering_vpcs information of the node in the database.
        local_vpc_db = []
        sync_routers = []

        sync_vpcs = []
        # Get metering used on this node
        for vpc in metering_vpcs:
            nodes = vpc['hostname']
            if self.hostname in nodes:
                db_routers.append(vpc['router_id'])
                local_vpc_db.append(vpc)
        # Get all routers which need to sync
        if db_routers:
            db_routers = set(db_routers)
            if ns_routers:
                ns_routers = set(ns_routers)
                sync_router = db_routers.symmetric_difference(ns_routers)
                sync_routers = list(sync_router)
            else:
                sync_routers = list(db_routers)
        else:
            if ns_routers:
                ns_routers = set(ns_routers)
                sync_routers = list(ns_routers)
        if sync_routers:
            for meter in local_vpc_db:
                if meter['router_id'] in sync_routers:
                    sync_vpcs.append((meter))
        if sync_vpcs:
            self.sync_metering_vpc_routers(context, sync_vpcs)

    def metering_vm(self):
        LOG.info('Vm counter loop task starting, self.vmKafkaManager=%s ',
                 self.vmKafkaManager)

        ts = timeutils.utcnow_ts()
        tbegin = time.time()
        delta = ts - self.vm_last_report
        if delta >= self.conf.report_vm_interval:
            self.vmKafkaManager = kafka_mgt.VmKafkaManager()
            self.vmKafkaManager.reconnect_kafa()
            tend = time.time()
            LOG.info('Vm loop, delta=%s, kafka_dict=%s, escaped=%s',
                     delta, self.vmKafkaManager.kafka_dict,
                     (tend - tbegin))

            vm_instance = collect_dhcp.MonitorVm(self.vmKafkaManager)

            try:
                vm_instance.monitor_resource_vm(dhcpvm_cnt_log)
            except kafka_exc.NoBrokersAvailableError:
                LOG.warning('Reinit Kafka client in monitor Vm')
                self.initKafa = True
            except (kafka_exc.SocketDisconnectedError,
                    kafka_exc.LeaderNotAvailable) as ex:
                LOG.warning("Kafka connection has lost, "
                            "Reconnect in Vm loop, ex=%s ", ex)
                self.initKafa = True
            except Exception as e:
                LOG.error('Send failed in Vm loop, e=%s', e)

            texit = time.time()
            escaped_time = texit - tbegin
            LOG.info('Vm loop exit, escaped=%s sec', escaped_time)
            self.vm_last_report = ts
            if escaped_time >= self.conf.report_interval:
                LOG.warning("Found handle tcp conn time escape %s",
                            escaped_time)

    def _conver_extend_format(self, extend_dic):
        extend_list = []
        for key, value in extend_dic.items():
            extend = {}
            extend['rule_uuid'] = key
            extend['name'] = value.get('name', None)
            extend['direction'] = value.get('direction', None)
            extend['pkgs'] = value.get('pkgs', 0)
            extend['bytes'] = value.get('bytes', 0)
            extend_list.append(extend)
        return extend_list

    def _conver_extend_str(self, extend_dic):
        local_extend_str = ''
        for key, value in extend_dic.items():
            temp_extend_str = (
                '{rule_uuid}+'
                '{name}+'
                '{direction}+'
                '{pkgs}+'
                '{bytes}+'.format(
                    rule_uuid=key,
                    name=value.get('name', None),
                    direction=value.get('direction', None),
                    pkgs=value.get('pkgs', 0),
                    bytes=value.get('bytes', 0)
                ))
            local_extend_str += temp_extend_str
        return local_extend_str

    def _metering_notification(self):
        LOG.debug("--3 metering_infos.items=%s", self.metering_infos.items())

        time_begin = time.time()
        for label_id, info in self.metering_infos.items():
            data = {'label_id': label_id,
                    'tenant_id': info['tenant_id'],
                    'FIP_ip': info['FIP_ip'],
                    'FIP_uuid': info['FIP_uuid'],
                    'qos_uuid': info['qos_uuid'],
                    'vm_ip': info['vm_ip'],
                    'vm_uuid': info['vm_uuid'],
                    'vm_portid': info['vm_portid'],
                    'external_in_pkts': info['ingress_pkts'],
                    'external_in_bytes': info['ingress_bytes'],
                    'external_out_pkts': info['egress_pkts'],
                    'external_out_bytes': info['egress_bytes'],
                    'extend': self._conver_extend_format(info['extend']),
                    'duration_time': info['time'],
                    'first_update': info['first_update'],
                    'timestamp': info['last_update'],
                    'hostname': self.host,
                    'type': info['type'],
                    'tag': info['tag']}

            if info['type'] not in (n_const.METERING_TYPE_SNAT_EIP,
                                    n_const.METERING_TYPE_MULTI_EIP) and \
                    not info['vm_portid']:
                LOG.warning("Metering lable=%s should be removed, "
                            "cause FIP has been disassociated with vm.",
                            label_id)
                continue

            eip_str = ''

            try:
                eip_counter_obj = {'topic_eip': data}
                eip_str = jsonutils.dumps(
                    eip_counter_obj, ensure_ascii=False, indent=1)
                try:
                    self.eipKafkaManager.kafka_dict[
                        'producer_monitor_topic_eip'].produce(bytes(eip_str))
                except Exception as e:
                    LOG.error("Kafka produce error: %s", e)
                    self.initKafa = True
                self.eipKafkaManager.eip_counter += 1

                local_store_str = (
                    '{timestamp}+{tenant_id}+'
                    '{FIP_ip}+{hostname}+'
                    '{FIP_uuid}+{qos_uuid}+{vm_ip}+{label_id}+'
                    '{vm_uuid}+{vm_portid}+'
                    '{external_in_pkts}+{external_in_bytes}+'
                    '{external_out_pkts}+{external_out_bytes}+'
                    '{extend}'
                    '{duration_time}+{first_update}+'
                    '{type}+{tag}'.format(
                        timestamp=info['last_update'],
                        tenant_id=info['tenant_id'],
                        FIP_ip=info['FIP_ip'],
                        hostname=self.hostname,
                        FIP_uuid=info['FIP_uuid'],
                        qos_uuid=info['qos_uuid'],
                        vm_ip=info['vm_ip'],
                        label_id=label_id,
                        vm_uuid=info['vm_uuid'],
                        vm_portid=info['vm_portid'],
                        external_in_pkts=info['ingress_pkts'],
                        external_in_bytes=info['ingress_bytes'],
                        external_out_pkts=info['egress_pkts'],
                        external_out_bytes=info['egress_bytes'],
                        extend=self._conver_extend_str(info['extend']),
                        duration_time=info['time'],
                        first_update=info['first_update'],
                        type=info['type'],
                        tag=info['tag']))

                eip_cnt_log.logger.info(local_store_str)

            except kafka_exc.SocketDisconnectedError:
                LOG.warning(
                    "Kafka fip connection has lost, "
                    "reconnect and resending...")
                self.initKafa = True

            except kafka_exc.NoBrokersAvailableError:
                LOG.warning('Re-init Kafka client in fip')
                self.initKafa = True

            except kafka_exc.UnknownError:
                self.initKafa = True
                LOG.error('Send failed in eip loop')

            except Exception as e:
                LOG.exception('Re-init Eip Kafka client in fip for: %s', e)
                self.initKafa = True

            # notifier = n_rpc.get_notifier('metering')
            # notifier.info(self.context, 'l3.meter', data)
            info['ingress_pkts'] = 0
            info['ingress_bytes'] = 0
            info['egress_pkts'] = 0
            info['egress_bytes'] = 0
            info['time'] = 0
            info['extend'] = {}

        escaped_time = time.time() - time_begin
        LOG.info("Eip_counter=%s, escaped=%s",
                 self.eipKafkaManager.eip_counter, escaped_time)
        if escaped_time > self.conf.report_interval:
            LOG.warning('Report EIP time escaped %s', escaped_time)

    def _purge_metering_info(self):
        deadline_timestamp = timeutils.utcnow_ts() - self.conf.report_interval
        label_ids = [
            label_id
            for label_id, info in self.metering_infos.items()
            if info['last_update'] < deadline_timestamp]
        for label_id in label_ids:
            del self.metering_infos[label_id]

    def _add_metering_info(self, label_id, label_data):
        ts = timeutils.utcnow_ts()
        info = self.metering_infos.get(label_id,
                                       {'ingress_pkts': 0,
                                        'ingress_bytes': 0,
                                        'egress_pkts': 0,
                                        'egress_bytes': 0,
                                        'extend': {},
                                        'time': 0,
                                        'first_update': ts,
                                        'last_update': ts,
                                        'vm_uuid': '',
                                        'vm_ip': '',
                                        'vm_portid': '',
                                        'FIP_uuid': '',
                                        'FIP_ip': '',
                                        'qos_uuid': '',
                                        'tenant_id': '',
                                        'type': n_const.METERING_TYPE_EIP,
                                        'tag': None})

        for curt in label_data['rules_traffic']:
            if not curt.get('address_group_id', None):
                if curt['direction'] == constants.INGRESS_DIRECTION:
                    info['ingress_pkts'] += curt['pkts']
                    info['ingress_bytes'] += curt['bytes']
                    continue
                if curt['direction'] == constants.EGRESS_DIRECTION:
                    info['egress_pkts'] += curt['pkts']
                    info['egress_bytes'] += curt['bytes']
                    continue
            else:
                rule_info = info['extend'].get(curt['id'],
                                               {'name': '',
                                                'direction': '',
                                                'pkgs': 0,
                                                'bytes': 0})
                rule_info['name'] = curt['address_group_name']
                rule_info['direction'] = curt['direction']
                rule_info['pkgs'] += curt['pkts']
                rule_info['bytes'] += curt['bytes']
                info['extend'][curt['id']] = rule_info

        info['time'] += ts - info['last_update']
        LOG.debug('-- add_metering_info ts=%s - info[last_update]=%s --',
                  ts, info['last_update'])
        info['last_update'] = ts
        LOG.debug('-- add_metering_info info[last_update]=%s --',
                  info['last_update'])

        report_info = self.label_report_info.get(label_id, None)
        if report_info:
            info['vm_uuid'] = report_info['vm_uuid']
            info['vm_portid'] = report_info['vm_portid']
            info['vm_ip'] = report_info['vm_ip']
            info['FIP_uuid'] = report_info['FIP_uuid']
            info['FIP_ip'] = report_info['FIP_ip']
            info['qos_uuid'] = report_info['qos_uuid']
            info['tenant_id'] = report_info['tenant_id']
            info['type'] = report_info['type']
            info['tag'] = report_info['tag']
        else:
            LOG.warning('Not found label id=%s report node.', label_id)

        self.metering_infos[label_id] = info

        return info

    def _add_metering_infos(self):
        label_id_set = set()
        self.label_report_info = {}

        for router in self.routers.values():
            tenant_id = router['tenant_id']
            labels = router.get(n_const.METERING_LABEL_KEY, [])
            for label in labels:
                tag = None
                for meter_r in self.metering_router_vpc['router']:
                    if meter_r['id'] == router['id']:
                        tag = meter_r['status']
                fip_db = self.get_metering_fip(
                    self.context, label.get('FIP_uuid'))
                if not fip_db:
                    LOG.warning(
                        "Floatingip is None value %s", label.get('FIP_uuid'))
                    fip_db = {}
                report_fip_dict = {
                    'tenant_id': tenant_id,
                    'vm_ip': label.get('vm_ip'),
                    'vm_uuid': label.get('vm_uuid'),
                    'ipversion': label.get('ipversion'),
                    'vm_portid': label.get('vm_portid'),
                    'FIP_uuid': label.get('FIP_uuid'),
                    'FIP_ip': label.get('FIP_ip'),
                    'qos_uuid': fip_db.get('qos_policy_id', None),
                    'type': label.get('type'),
                    'tag': tag}
                label_id = label['id']
                label_id_set.add(label_id)
                self.label_report_info[label_id] = report_fip_dict

        accs = self._get_traffic_counters(self.context, self.routers.values())

        if not accs:
            LOG.warning('--add_metering_infos no accs--')
            return

        for label_id, acc in accs.items():
            if label_id in label_id_set:
                self._add_metering_info(label_id, acc)
            else:
                LOG.warning(' Drop label %s collecting data.', label_id)

    def _metering_loop(self):
        try:
            LOG.info('Eip loop starting, self.initKafa=%s ', self.initKafa)

            self._sync_router_namespaces(self.context, self.routers.values())
            LOG.debug('--1 add_metering_infos--')
            self._add_metering_infos()

            ts = timeutils.utcnow_ts()
            delta = ts - self.last_report

            report_interval = self.conf.report_interval
            LOG.debug('--2 delta=%s report_interval=%s '
                      'currenttime=%s self.last_report=%s--',
                      delta, report_interval,
                      ts, self.last_report)
            tbegin = time.time()
            if delta >= report_interval:
                self.eipKafkaManager = kafka_mgt.EipKafkaManager()
                self.eipKafkaManager.reconnect_kafa()
                tend = time.time()

                LOG.info(
                    'Eip loop, delta=%s, self.initKafa=%s  %s, escaped=%s',
                    delta, self.initKafa, self.eipKafkaManager.kafka_dict,
                    (tend - tbegin))
                if not self.eipKafkaManager.kafka_dict['initSucc']:
                    LOG.info('Eip loop init kafak failed')
                    return
                self._metering_notification()
                self._purge_metering_info()
                self.last_report = ts
                LOG.info("last_report=%s  escaped=%s",
                         self.last_report, (time.time() - self.last_report))
        except Exception as e:
            LOG.error('Eip loop failed, e=%s', e)

    @runtime.synchronized('metering-agent')
    def _invoke_driver(self, context, meterings, func_name):
        try:
            return getattr(self.metering_driver, func_name)(context, meterings)
        except AttributeError:
            LOG.exception("Driver %(driver)s does not implement %(func)s",
                          {'driver': self.conf.driver,
                           'func': func_name})
        except RuntimeError:
            LOG.exception("Driver %(driver)s:%(func)s runtime error",
                          {'driver': self.conf.driver,
                           'func': func_name})

    @periodic_task.periodic_task(run_immediately=True)
    def _sync_routers_task(self, context):
        if self.enable_sdn:
            return

        t1 = time.time()
        routers = self.get_sync_data_metering(self.context)
        if not routers:
            LOG.info('===sync_routers_task===: no metering labels')
        else:
            LOG.debug('====sync_routers_task: escap:%s', time.time() - t1)
        routers_on_agent = set(self.routers.keys())
        routers_on_server = set(
            [router['id'] for router in routers] if routers else [])
        for router_id in routers_on_agent - routers_on_server:
            del self.routers[router_id]
            self._invoke_driver(context, router_id, 'remove_router')

        if not routers:
            return
        self._update_routers(context, routers)

    def router_deleted(self, context, router_id):
        if self.metering_router_vpc['router']:
            routers = self.metering_router_vpc['router']
            for metering_router in routers:
                if router_id == metering_router['id']:
                    self.metering_router_vpc['router'].remove(metering_router)
                    break
        self._add_metering_infos()
        LOG.debug('MeteringAgent:router_deleted:vpc_and_router_info %s',
                  self.metering_router_vpc)
        if router_id in self.routers:
            del self.routers[router_id]

        return self._invoke_driver(context, router_id,
                                   'remove_router')

    def update_metering(self, context, router):
        LOG.debug("Start to update metering router %s.", router)
        LOG.info("Agent routers %s.", self.routers.keys())
        action = router.get('action')
        if action == 'clean':
            self.clean_metering_label_rules(context, router)
        if action == 'update':
            self.sync_router_metering(context, router)

    def routers_updated(self, context, routers=None):
        if not routers:
            routers = self.get_sync_data_metering(self.context)
        if not routers:
            return
        self._update_routers(context, routers)

    def _update_routers(self, context, routers):
        for router in routers:
            self.routers[router['id']] = router

        return self._invoke_driver(context, routers,
                                   'update_routers')

    def _get_traffic_counters(self, context, routers):
        LOG.debug("Get router traffic counters")
        return self._invoke_driver(
            context, routers,
            'get_rules_traffic_counters')

    def _sync_router_namespaces(self, context, routers):
        LOG.debug("Sync router namespaces")
        return self._invoke_driver(context, routers, 'sync_router_namespaces')

    def add_metering_label_rule(self, context, routers):
        LOG.debug("Add a metering label rule from agent, routers = %s",
                  routers)
        return self._invoke_driver(context, routers,
                                   'add_metering_label_rule')

    def remove_metering_label_rule(self, context, routers):
        LOG.debug("Remove a metering label rule from agent, routers = %s",
                  routers)
        return self._invoke_driver(context, routers,
                                   'remove_metering_label_rule')

    def update_metering_label_rules(self, context, routers):
        LOG.debug("Update metering rules from agent, routers = %s",
                  routers)
        return self._invoke_driver(context, routers,
                                   'update_metering_label_rules')

    def add_metering_label(self, context, routers):
        LOG.debug("Creating a metering label from agent")
        return self._invoke_driver(context, routers,
                                   'add_metering_label')

    def remove_metering_label(self, context, routers):
        self._add_metering_infos()

        LOG.debug("Delete a metering label from agent")
        return self._invoke_driver(context, routers,
                                   'remove_metering_label')

    def clean_metering_router_iptable(self, context, routers):
        LOG.debug("Clean iptables for EIP from agent")
        return self._invoke_driver(context, routers,
                                   'clean_metering_router_iptable')

    def clean_metering_one_router_iptable(self, context, router_id):
        LOG.debug("Clean iptables for one router from agent")
        return self._invoke_driver(context, router_id,
                                   'clean_metering_one_router_iptable')

    def clean_metering_label_rules(self, context, router):
        LOG.info("Clean metering label rules with label:%s.",
                 router.get('label_id'))
        return self._invoke_driver(context, router,
                                   'clean_metering_label_rules')

    def add_router_meter(self, context, router, host):
        if self.host == host and router:
            meter_router_ids = []
            for meter_r in self.metering_router_vpc['router']:
                if meter_r['id'] not in meter_router_ids:
                    meter_router_ids.append(meter_r['id'])
            router_meter = {'id': router['id'],
                            'ha': router['ha'],
                            'status': None}
            if router_meter['id'] not in meter_router_ids:
                self.metering_router_vpc['router'].append(router_meter)
                LOG.debug('Agent:add_router:metering_router %s', router_meter)
                LOG.debug('Agent:add_router:metering_router_vpc %s',
                          self.metering_router_vpc)

    def monitor_metering_vpc(self, context, metering_vpc):
        # Update metering vpc
        vpc_obj = collect_vpc.MonitorVPC()
        all_routers = vpc_obj._get_all_routers()
        self._sync_metering_vpc_routers(context, all_routers, metering_vpc)
        vpc_obj.monitor_metering_vpc_to_vpc(
            vpc_to_vpc_log, metering_vpc)

    def add_metering_vpc(self, context, metering_vpc):
        try:
            if metering_vpc:
                if not isinstance(metering_vpc['hostname'], list):
                    metering_vpc['hostname'] = [
                        i for i in metering_vpc['hostname'].split(',')]
                if self.hostname in metering_vpc['hostname']:
                    added = True
                    for vpc2vpc in self.metering_router_vpc['vpc2vpc']:
                        if vpc2vpc['id'] == metering_vpc['id']:
                            added = False
                    if added:
                        self.metering_router_vpc['vpc2vpc'].append(
                            metering_vpc)
            LOG.debug('Agent:add_vpc:metering_vpc2vpc %s', metering_vpc)
            LOG.debug('Agent:add_vpc:metering_router_vpc %s',
                      self.metering_router_vpc)
        except Exception as e:
            LOG.exception('Failed to add metering_vpc2vpc %s '
                          'on this node,reason %s',
                          metering_vpc, e)

    def remove_metering_vpc(self, context, metering_vpc):
        try:
            if metering_vpc:
                # Delete vpc2vpc account meter_vpc_id
                # or router_id[+destination_ip]
                for vpc in metering_vpc:
                    if not isinstance(vpc['hostname'], list):
                        vpc['hostname'] = [
                            i for i in vpc['hostname'].split(',')]
                    if self.hostname in vpc['hostname']:
                        for vpc2vpc in self.metering_router_vpc['vpc2vpc']:
                            if vpc['id'] == vpc2vpc['id']:
                                self.metering_router_vpc['vpc2vpc'].remove(
                                    vpc2vpc)
                                LOG.debug('Agent remove metering vpc2vpc %s',
                                          vpc2vpc)
                                break
                LOG.debug('Agent:remove:metering_vpc2vpc %s', metering_vpc)
                LOG.debug('Agent:removed:metering_router_vpc %s',
                          self.metering_router_vpc)
        except Exception as e:
            LOG.exception('Failed to remove metering_vpc2vpc %s', e)

    def update_metering_vpc(self, context, metering_vpc):
        # Update metering vpc
        try:
            for vpc2vpc in self.metering_router_vpc['vpc2vpc']:
                if vpc2vpc['id'] == metering_vpc['id']:
                    if self.hostname in metering_vpc['hostname']:
                        self.metering_router_vpc['vpc2vpc'].remove(vpc2vpc)
                        self.metering_router_vpc['vpc2vpc'].append(
                            metering_vpc)
                        break
                    else:
                        self.metering_router_vpc['vpc2vpc'].remove(vpc2vpc)
                        break
                else:
                    if self.hostname in metering_vpc['hostname']:
                        self.metering_router_vpc['vpc2vpc'].append(
                            metering_vpc)
                        break
                    else:
                        continue
            LOG.debug('Agent:updated:metering_vpc2vpc %s',
                      self.metering_router_vpc)
        except Exception:
            LOG.error('Failed to update metering_vpc2vpc %s on this node',
                      metering_vpc)

    def sync_metering_router_vpc2vpc(self):
        # Sync router and vpc2vpc with db on this node every 10 mins.
        ts = timeutils.utcnow_ts()
        delta = ts - self.router_vpc_last_report
        tbegin = time.time()
        if delta >= self.sync_router_vpc_interval:
            try:
                LOG.info('Sync router vpc2vpc loop task staring.')
                if not self.enable_sdn:
                    self.metering_router_vpc = (
                        self.get_plugin_metering_router_vpc(
                            self.context, flag='all'))
                else:
                    self.metering_router_vpc = (
                        self.get_plugin_metering_router_vpc(
                            self.context, flag='router'))
                tend = time.time()
                LOG.info('Sync router vpc2vpc loop task exit, %s, '
                         'escaped=%s',
                         len(self.metering_router_vpc['router']),
                         (tend - tbegin))
                self.router_vpc_last_report = ts

            except oslo_messaging.MessagingTimeout:
                self.init_vpc2vpc = False
                self.metering_router_vpc['vpc2vpc'] = []
                LOG.error('Failed sync router vpc2vpc from neutron-server')
            except Exception as e:
                LOG.exception('sync Rv failed %s', e)


class MeteringAgentWithStateReport(MeteringAgent):

    def __init__(self, host, conf=None):
        super(MeteringAgentWithStateReport, self).__init__(host=host,
                                                           conf=conf)
        self.state_rpc = agent_rpc.PluginReportStateAPI(topics.REPORTS)
        self.failed_report_state = False
        self.agent_state = {
            'binary': 'neutron-metering-agent',
            'host': host,
            'topic': topics.METERING_AGENT,
            'configurations': {
                'metering_driver': self.conf.driver,
                'measure_interval':
                    self.conf.measure_interval,
                'report_interval': self.conf.report_interval
            },
            'start_flag': True,
            'agent_type': constants.AGENT_TYPE_METERING}
        report_interval = cfg.CONF.AGENT.report_interval
        self.use_call = True
        if report_interval:
            self.heartbeat = loopingcall.FixedIntervalLoopingCall(
                self._report_state)
            self.heartbeat.start(interval=report_interval)

    def _report_state(self):
        try:
            self.state_rpc.report_state(self.context, self.agent_state,
                                        self.use_call)
            self.agent_state.pop('start_flag', None)
            self.use_call = False
        except AttributeError:
            # This means the server does not support report_state
            LOG.warning("Neutron server does not support state report. "
                        "State report for this agent will be disabled.")
            self.heartbeat.stop()
        except Exception:
            self.failed_report_state = True
            LOG.exception("Failed reporting state!")
            return
        if self.failed_report_state:
            self.failed_report_state = False
            LOG.info("Successfully reported state after a previous failure.")

    def agent_updated(self, context, payload):
        LOG.info("agent_updated by server side %s!", payload)


def main():
    conf = cfg.CONF
    metering_agent.register_metering_agent_opts()
    config.register_agent_state_opts_helper(conf)
    common_config.init(sys.argv[1:])
    config.setup_logging()
    config.setup_privsep()

    procManager = pm.ProcessManager()
    procManager.monitor_start()

    server = neutron_service.Service.create(
        binary='neutron-metering-agent',
        topic=topics.METERING_AGENT,
        report_interval=cfg.CONF.AGENT.report_interval,
        manager='neutron.services.metering.agents.'
                'metering_agent.MeteringAgentWithStateReport')

    service.launch(cfg.CONF, server, restart_method='mutate').wait()
    del procManager
