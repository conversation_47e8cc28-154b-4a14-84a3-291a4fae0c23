# Copyright 2011 VMware, Inc.
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.utils import net
from oslo_config import cfg
from oslo_service import wsgi

from neutron._i18n import _
from neutron.common import constants


core_opts = [
    cfg.HostAddressOpt('bind_host', default='0.0.0.0',
                       help=_("The host IP to bind to.")),
    cfg.PortOpt('bind_port', default=9696,
                help=_("The port to bind to")),
    cfg.StrOpt('api_extensions_path', default="",
               help=_("The path for API extensions. "
                      "Note that this can be a colon-separated list of paths. "
                      "For example: api_extensions_path = "
                      "extensions:/path/to/more/exts:/even/more/exts. "
                      "The __path__ of neutron.extensions is appended to "
                      "this, so if your extensions are in there you don't "
                      "need to specify them here.")),
    cfg.StrOpt('auth_strategy', default='keystone',
               help=_("The type of authentication to use")),
    cfg.BoolOpt('enable_meter_full_eip', default=True,
                help=_("Enable metering full floating ip triggeged by "
                       "service.")),
    cfg.BoolOpt('enable_meter_port_forwarding', default=True,
                help=_("Enable metering port forwarding floating ip triggeged "
                       "by service.")),
    cfg.BoolOpt('enable_meter_ecs_ipv6', default=True,
                help=_("Enable metering ecs_ipv6 floating ip triggeged by "
                       "service.")),
    cfg.BoolOpt('enable_meter_snat_eip', default=True,
                help=_("Enable metering elastic snat floating ip triggeged by "
                       "service.")),
    cfg.BoolOpt('enable_meter', default=True,
                help=_("Enable metering service plugin.")),
    cfg.BoolOpt('is_sdn_arch', default=False,
                help=_("True when deploying SDN.")),
    cfg.StrOpt('core_plugin',
               help=_("The core plugin Neutron will use")),
    cfg.ListOpt('service_plugins', default=[],
                help=_("The service plugins Neutron will use")),
    cfg.StrOpt('base_mac', default="fa:16:3e:00:00:00",
               help=_("The base MAC address Neutron will use for VIFs. "
                      "The first 3 octets will remain unchanged. If the 4th "
                      "octet is not 00, it will also be used. The others "
                      "will be randomly generated.")),
    cfg.BoolOpt('allow_bulk', default=True,
                help=_("Allow the usage of the bulk API")),
    cfg.StrOpt('pagination_max_limit', default="-1",
               help=_("The maximum number of items returned in a single "
                      "response, value was 'infinite' or negative integer "
                      "means no limit")),
    cfg.ListOpt('default_availability_zones', default=[],
                help=_("Default value of availability zone hints. The "
                       "availability zone aware schedulers use this when "
                       "the resources availability_zone_hints is empty. "
                       "Multiple availability zones can be specified by a "
                       "comma separated string. This value can be empty. "
                       "In this case, even if availability_zone_hints for "
                       "a resource is empty, availability zone is "
                       "considered for high availability while scheduling "
                       "the resource.")),
    cfg.ListOpt('default_network_az_hints', default=[],
                help=_("Default availability_zone_hints when creating a "
                       "network without specifying an availability_zone.")),
    cfg.ListOpt('default_router_az_hints', default=[],
                help=_("Default availability_zone_hints when creating "
                       "router without specifying an availability_zone.")),
    cfg.BoolOpt('allow_no_valid_az', default=True,
                help=_("allow pass no valid availability_zone "
                       "to create router")),
    cfg.IntOpt('max_dns_nameservers', default=5,
               help=_("Maximum number of DNS nameservers per subnet")),
    cfg.IntOpt('max_subnet_host_routes', default=20,
               help=_("Maximum number of host routes per subnet")),
    cfg.BoolOpt('ipv6_pd_enabled', default=False,
                help=_("Enables IPv6 Prefix Delegation for automatic subnet "
                       "CIDR allocation. "
                       "Set to True to enable IPv6 Prefix Delegation for "
                       "subnet allocation in a PD-capable environment. Users "
                       "making subnet creation requests for IPv6 subnets "
                       "without providing a CIDR or subnetpool ID will be "
                       "given a CIDR via the Prefix Delegation mechanism. "
                       "Note that enabling PD will override the behavior of "
                       "the default IPv6 subnetpool.")),
    cfg.IntOpt('dhcp_lease_duration', default=86400,
               help=_("DHCP lease duration (in seconds). Use -1 to tell "
                      "dnsmasq to use infinite lease times.")),
    cfg.StrOpt('dns_domain',
               default='openstacklocal',
               help=_('Domain to use for building the hostnames')),
    cfg.StrOpt('external_dns_driver',
               help=_('Driver for external DNS integration.')),
    cfg.BoolOpt('dhcp_agent_notification', default=True,
                help=_("Allow sending resource operation"
                       " notification to DHCP agent")),
    cfg.BoolOpt('allow_overlapping_ips', default=False,
                help=_("Allow overlapping IP support in Neutron. "
                       "Attention: the following parameter MUST be set to "
                       "False if Neutron is being used in conjunction with "
                       "Nova security groups.")),
    cfg.HostAddressOpt('host', default=net.get_hostname(),
                       sample_default='example.domain',
                       help=_("Hostname to be used by the Neutron server, "
                              "agents and services running on this machine. "
                              "All the agents and services running on this "
                              "machine must use the same host value.")),
    cfg.StrOpt("network_link_prefix",
               help=_("This string is prepended to the normal URL that is "
                      "returned in links to the OpenStack Network API. If it "
                      "is empty (the default), the URLs are returned "
                      "unchanged.")),
    cfg.BoolOpt('notify_nova_on_port_status_changes', default=True,
                help=_("Send notification to nova when port status changes")),
    cfg.BoolOpt('notify_nova_on_port_data_changes', default=True,
                help=_("Send notification to nova when port data (fixed_ips/"
                       "floatingip) changes so nova can update its cache.")),
    cfg.IntOpt('send_events_interval', default=2,
               help=_('Number of seconds between sending events to nova if '
                      'there are any events to send.')),
    cfg.StrOpt('setproctitle', default='on',
               help=_("Set process name to match child worker role. "
                      "Available options are: 'off' - retains the previous "
                      "behavior; 'on' - renames processes to "
                      "'neutron-server: role (original string)'; "
                      "'brief' - renames the same as 'on', but without the "
                      "original string, such as 'neutron-server: role'.")),
    cfg.StrOpt('ipam_driver', default='internal',
               help=_("Neutron IPAM (IP address management) driver to use. "
                      "By default, the reference implementation of the "
                      "Neutron IPAM driver is used.")),
    cfg.BoolOpt('vlan_transparent', default=False,
                help=_('If True, then allow plugins that support it to '
                       'create VLAN transparent networks.')),
    cfg.BoolOpt('filter_validation', default=True,
                help=_('If True, then allow plugins to decide '
                       'whether to perform validations on filter parameters. '
                       'Filter validation is enabled if this config'
                       'is turned on and it is supported by all plugins')),
    cfg.IntOpt('global_physnet_mtu', default=constants.DEFAULT_NETWORK_MTU,
               deprecated_name='segment_mtu', deprecated_group='ml2',
               help=_('MTU of the underlying physical network. Neutron uses '
                      'this value to calculate MTU for all virtual network '
                      'components. For flat and VLAN networks, neutron uses '
                      'this value without modification. For overlay networks '
                      'such as VXLAN, neutron automatically subtracts the '
                      'overlay protocol overhead from this value. Defaults '
                      'to 1500, the standard value for Ethernet.')),
    cfg.StrOpt('wo_default_qos_for_fip', default='',
               help=_('when creating a floating ip, but qos_policy_id is not '
                      'specified, the floating ip will specify the default '
                      'qos policy id.')),
    cfg.BoolOpt('wo_use_project_default_qos_for_fip', default=False,
               help=_('If True, then allow using the project default qos '
                      'when creating a new floating ip.')),
    cfg.ListOpt('default_ipv6_denied_ports', default=[80, 443, 8080],
                help=_('default ipv6 eip denied port numbers.')),
    cfg.ListOpt('subnet_default_dns_nameservers', default=[],
                help=_("Default value of ipv4 subnet's dns nameserver")),
    cfg.ListOpt('subnet_default_dns_nameservers_v6', default=[],
                help=_("Default value of ipv6 subnet's dns nameserver")),
    cfg.BoolOpt('enable_traditional_dhcp', default=True,
                help=_('If False, neutron-server will disable the following '
                       'DHCP-agent related functions:'
                       '1. DHCP provisioning block '
                       '2. DHCP scheduler API extension '
                       '3. Network scheduling mechanism '
                       '4. DHCP RPC/notification')),
    cfg.BoolOpt('enable_dhcp_provisioning_block', default=True,
                help=_('If False, neutron-server will disable the following '
                       'DHCP-agent DHCP provisioning block')),
    cfg.BoolOpt('enable_update_network_type', default=False,
                help=_('If True, neutron-server will support update network '
                       'type from vxlan to vlan')),
    cfg.BoolOpt('enable_random_allocation_for_segment', default=True,
                help=_('If False, neutron-server will allocate segmentation '
                       'ids in order when creating tenant networks')),
    cfg.ListOpt('mechanism_call_logs', default=[],
                help=_("Mechanism driver call logs name and method list, "
                       "for instance "
                       "OpenvswitchMechanismDriver:update_port_precommit, "
                       "`:` means all logs for all type of drivers and "
                       "calls, `:update_port_precommit` means all "
                       "update_port_precommit calls of "
                       "any type of mechanism driver.")),
    cfg.ListOpt('notification_callback_logs', default=[],
                help=_("Notification callback logs callback function name, "
                       "resource, and event, for instance "
                       "_notify_gateway_port_ip_changed:port:after_update. "
                       "`::` means all logs for all type of notifications "
                       "callbacks;  `:port:` means all callback for "
                       "resource port ")),
    cfg.ListOpt('availability_zones', default=[],
                help=_('Default availability zone for sdn router.')),
    cfg.ListOpt('mtu_range', default=['96:8000'],
                help=_('Valid mtu range. '
                       'Comma-separated list of <min_mtu>:<max_mtu>.'))
]

core_cli_opts = [
    cfg.StrOpt('state_path',
               default='/var/lib/neutron',
               help=_("Where to store Neutron state files. "
                      "This directory must be writable by the agent.")),
]


def register_core_common_config_opts(cfg=cfg.CONF):
    cfg.register_opts(core_opts)
    cfg.register_cli_opts(core_cli_opts)
    wsgi.register_opts(cfg)


NOVA_CONF_SECTION = 'nova'

nova_opts = [
    cfg.StrOpt('region_name',
               help=_('Name of nova region to use. Useful if keystone manages'
                      ' more than one region.')),
    cfg.StrOpt('endpoint_type',
               default='public',
               choices=['public', 'admin', 'internal'],
               help=_('Type of the nova endpoint to use.  This endpoint will'
                      ' be looked up in the keystone catalog and should be'
                      ' one of public, internal or admin.')),
]


def register_nova_opts(cfg=cfg.CONF):
    cfg.register_opts(nova_opts, group=NOVA_CONF_SECTION)


PLACEMENT_CONF_SECTION = 'placement'

placement_opts = [
    cfg.StrOpt('region_name',
               help=_('Name of placement region to use. Useful if keystone '
                      'manages more than one region.')),
    cfg.StrOpt('endpoint_type',
               default='public',
               choices=['public', 'admin', 'internal'],
               help=_('Type of the placement endpoint to use.  This endpoint '
                      'will be looked up in the keystone catalog and should '
                      'be one of public, internal or admin.')),
]


def register_placement_opts(cfg=cfg.CONF):
    cfg.register_opts(placement_opts, group=PLACEMENT_CONF_SECTION)


PRIVATEFLOATING_CONF_SECTION = 'privatefloating'

privatefloating_opts = [
    cfg.BoolOpt('enable_privatefloating', default=False,
                help=_('If True, then allow privatefloating'
                       'when a port be created, additional ip '
                       'will be allocated '
                       'attach to the port from privatefloating subnet')),
    cfg.StrOpt('privatefloating_network', default='',
               help=_("The network uuid to be used for privatefloating ")),
    cfg.StrOpt('default_availability_zone', default='nova',
               help=_("The default availability zone "
                      "for privatefloating net")),
    cfg.IntOpt('arp_timeout', default=300,
               help=_("ARP responser flow exist time for aged, "
                      "unit is seconds")),
    cfg.ListOpt('additional_ingnored_deviceowners', default=[],
                help=_("Additional deviceowners to be "
                       "ignored when allocate private floating ip")),
    cfg.ListOpt('availability_zone_privatefloating_network',
                default=[],
                help=_("Comma-separated list of "
                       "<availability_zone_name>:<network_uuid> "
                       "tuples mapping availability zone names to the "
                       "privatefloating network.")),
    cfg.StrOpt('privatefloating_network_v6', default='',
               help=_("The network uuid to be used for privatefloating v6")),
    cfg.ListOpt('az_privatefloating_network_v6',
                default=[],
                help=_("Comma-separated list of "
                       "<availability_zone_name>:<network_uuid> "
                       "tuples mapping availability zone names to the "
                       "privatefloating v6 network.")),
]

IP_ALLOCATION_CONF_SECTION = 'ip_allocation'
ip_allocation_opts = [
    cfg.BoolOpt('bulk_allocation', default=False,
                help=_('If True, allow to allocate multiple address by the '
                       'same subnet_id one time, this will accelerate address'
                       ' assignment.')),
    cfg.IntOpt('bulk_number', default=10,
               help=_("Only the number of ip addresses is larger than this "
                      "value, this function will enable.")),
    cfg.IntOpt('max_bulk_number', default=100,
               help=_("Max number of ip address allowed to apply")),
]


def register_privatefloating_opts(cfg=cfg.CONF):
    cfg.register_opts(privatefloating_opts, group=PRIVATEFLOATING_CONF_SECTION)


def register_ip_allocation(cfg=cfg.CONF):
    cfg.register_opts(ip_allocation_opts, group=IP_ALLOCATION_CONF_SECTION)
