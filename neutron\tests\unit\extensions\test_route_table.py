#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import contextlib

from neutron_lib import context
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_utils import uuidutils
from webob import exc

from neutron.extensions import _route_table as apidef
from neutron.extensions import l3
from neutron.extensions import route_table as route_table_ext
from neutron.services.route_table.common import exceptions as rt_exc
from neutron.tests.unit.db import test_db_base_plugin_v2
from neutron.tests.unit.extensions import test_l3


_uuid = uuidutils.generate_uuid


class RouteTableTestExtensionManager(object):

    def get_resources(self):
        return (l3.L3.get_resources() +
                route_table_ext.Route_table.get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class TestRouteTableExtension(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
        test_l3.L3NatTestCaseMixin):

    def setUp(self, plugin=None, ext_mgr=None):
        svc_plugins = (
            'neutron.services.route_table.plugin.RouteTablePlugin',
            'neutron.services.l3_router.l3_router_plugin.L3RouterPlugin',
            'neutron.services.flavors.flavors_plugin.FlavorsPlugin'
        )
        ext_mgr = RouteTableTestExtensionManager()
        super(TestRouteTableExtension, self).setUp(
            plugin='neutron.plugins.ml2.plugin.Ml2Plugin',
            ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.rt_plugin = directory.get_plugin(apidef.ROUTE_TABLE)
        self.l3_plugin = directory.get_plugin(plugin_constants.L3)
        self.ctx = context.get_admin_context()
        cfg.CONF.set_default('max_route_tables', 10)
        cfg.CONF.set_default('max_route_table_routes', 200)
        cfg.CONF.set_default('supported_routes_nexthop_type', ['ecs', 'ipv4'])

    def _create_route_table(self, fmt, router_id, name='rt', routes=None,
                            description=None, tenant_id=None,
                            set_context=False, **kwargs):
        tenant_id = tenant_id or self._tenant_id

        data = {'route_table': {'name': name,
                                'router_id': router_id,
                                'tenant_id': tenant_id}}
        if routes:
            data['route_table']['routes'] = routes
        if description:
            data['route_table']['description'] = description
        for k, v in kwargs:
            data['route_table'][k] = v
        req = self.new_create_request('route_tables', data, fmt)
        if set_context and tenant_id:
            req.environ['neutron.context'] = context.Context(
                '', tenant_id)

        res = req.get_response(self.api)
        if res.status_int >= exc.HTTPClientError.code:
            raise exc.HTTPClientError(code=res.status_int)
        return self.deserialize(fmt, res)

    @contextlib.contextmanager
    def route_table(self, router_id, name='rt1', fmt=None, tenant_id=None,
                    routes=None, description=None, set_context=False,
                    **kwargs):
        route_table = self._create_route_table(
            fmt or self.fmt, router_id, name, routes, description,
            tenant_id, set_context, **kwargs)
        yield route_table

    def _route_table_routes_action(self, action, routes, route_table_id,
                                   expected_code=exc.HTTPOk.code,
                                   expected_body=None,
                                   tenant_id=None,
                                   msg=None):
        data = {'route_table': {
            'routes': routes}}

        req = self.new_action_request('route_tables', data, route_table_id,
                                      "%s_route_table_routes" % action)
        # if tenant_id was specified, create a tenant context for this request
        if tenant_id:
            req.environ['neutron.context'] = context.Context(
                '', tenant_id)
        res = req.get_response(self.ext_api)
        self.assertEqual(expected_code, res.status_int, msg)
        response = self.deserialize(self.fmt, res)
        if expected_body:
            self.assertEqual(expected_body, response, msg)
        return response

    def _action_subnets(self, action, subnets, route_table_id,
                        expected_code=exc.HTTPOk.code,
                        expected_body=None,
                        tenant_id=None,
                        msg=None,
                        default=False):
        data = {'route_table': {'subnets': subnets}}

        req = self.new_action_request('route_tables', data, route_table_id,
                                      "%s_subnets" % action)
        # if tenant_id was specified, create a tenant context for this request
        if tenant_id:
            req.environ['neutron.context'] = context.Context(
                '', tenant_id)
        res = req.get_response(self.ext_api)
        if default:
            self.assertEqual(expected_code, res.status_int, msg)
            return
        self.assertEqual(expected_code, res.status_int, msg)
        response = self.deserialize(self.fmt, res)
        if expected_body:
            self.assertEqual(expected_body, response, msg)
        return response

    def test_create_route_table(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                routes = [{'destination': '***********/16',
                           'nexthop': '********',
                           'type': 'ecs'}]
                rt = self._create_route_table(self.fmt, r['router']['id'],
                                              name='rt',
                                              description='test',
                                              routes=routes)['route_table']
                self.assertEqual('rt', rt['name'])
                self.assertEqual('test', rt['description'])
                self.assertEqual(r['router']['id'], rt['router_id'])
                self.assertEqual(routes, rt['routes'])

    def test_create_route_table_without_or_none_routes(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'],
                                              description='test',
                                              name='rt')['route_table']
                self.assertEqual('rt', rt['name'])
                self.assertEqual('test', rt['description'])
                self.assertEqual(r['router']['id'], rt['router_id'])
                self.assertEqual([], rt['routes'])

                none_rt = self._create_route_table(
                    self.fmt, r['router']['id'], routes=None)['route_table']
                self.assertEqual([], none_rt['routes'])

    def test_update_route_table(self):
        with self.router() as r:
            with self.route_table(r['router']['id']) as rt:
                data = {'route_table': {
                    'name': 'update',
                    'description': 'update'}}
                req = self.new_update_request('route_tables', data,
                                              rt['route_table']['id'])
                res = req.get_response(self.api)
                self.assertEqual(exc.HTTPOk.code, res.status_int)
                res = self.deserialize(self.fmt, res)
                self.assertEqual('update', res['route_table']['name'])
                self.assertEqual('update', res['route_table']['description'])

    def test_delete_route_table(self):
        with self.router() as r:
            with self.route_table(r['router']['id']) as rt:
                self._delete('route_tables', rt['route_table']['id'])
                self.assertRaises(
                    rt_exc.RouteTableNotFound,
                    self.rt_plugin.get_route_table,
                    context=self.ctx, id=rt['route_table']['id'])

    def test_create_default_route_table(self):
        with self.router() as r:
            default_id = self.rt_plugin._get_default_rt_id(
                self.ctx, r['router']['id'])
            self.assertIsNotNone(default_id)

    def test_add_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'],
                                              description='test',
                                              name='rt')['route_table']
                res = self._route_table_routes_action('add', routes, rt['id'])
                self.assertEqual(routes, res['routes'])

    def test_add_multi_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '********/8',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'],
                                              description='test',
                                              name='rt')['route_table']
                res = self._route_table_routes_action('add', routes, rt['id'])
                self.assertEqual(routes, res['routes'])

    def test_add_external_routes(self):
        my_tenant = 'tenant1'
        with self.subnet(cidr='********/24', tenant_id='123') as ext_subnet,\
            self.port(subnet=ext_subnet) as nexthop_port:
            nexthop_ip = nexthop_port['port']['fixed_ips'][0]['ip_address']
            routes = [{'destination': '***********/16',
                       'nexthop': nexthop_ip, 'type': 'ipv4'}]
            self._set_net_external(ext_subnet['subnet']['network_id'])
            ext_info = {'network_id': ext_subnet['subnet']['network_id']}
            with self.router(
                    external_gateway_info=ext_info, tenant_id=my_tenant) as r:
                rt = self._create_route_table(self.fmt, r['router']['id'])
                res = self._route_table_routes_action('add', routes,
                                                      rt['route_table']['id'])
                self.assertEqual(routes, res['routes'])

    def test_add_routes_multiple_routers(self):
        with self.router() as r1, self.router() as r2, \
                self.subnet(cidr='10.0.0.0/24') as s:
            with self.port(subnet=s) as p1, self.port(subnet=s) as p2:
                p1_ip = p1['port']['fixed_ips'][0]['ip_address']
                p2_ip = p2['port']['fixed_ips'][0]['ip_address']
                routes1 = [{'destination': '***********/16',
                            'nexthop': p2_ip, 'type': 'ipv4'}]
                routes2 = [{'destination': '********/8',
                            'nexthop': p1_ip, 'type': 'ipv4'}]
                self._router_interface_action(
                    'add', r1['router']['id'], None, p1['port']['id'])
                rt1 = self._create_route_table(self.fmt, r1['router']['id'])
                self._route_table_routes_action(
                    'add', routes1, rt1['route_table']['id'])
                res = self.rt_plugin.get_route_table(
                    self.ctx, rt1['route_table']['id'])
                self.assertEqual(routes1, res['routes'])

                self._router_interface_action(
                    'add', r2['router']['id'], None, p2['port']['id'])
                rt2 = self._create_route_table(self.fmt, r2['router']['id'])
                self._route_table_routes_action(
                    'add', routes2, rt2['route_table']['id'])
                res = self.rt_plugin.get_route_table(
                    self.ctx, rt2['route_table']['id'])
                self.assertEqual(routes2, res['routes'])

    def test_remove_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '********/8',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'}]
        remove = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '********/8',
                   'nexthop': '********', 'type': 'ipv4'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'],
                                              description='test',
                                              routes=routes,
                                              name='rt')['route_table']
                self.assertEqual(routes, rt['routes'])
                res = self._route_table_routes_action(
                    'remove', remove, rt['id'])
                routes.remove(remove[0])
                routes.remove(remove[1])
                self.assertNotIn(remove[0], routes)
                self.assertNotIn(remove[1], routes)
                self.assertEqual(routes, res['routes'])

    def _test_add_invalid_routes(self, routes):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                with self.route_table(r['router']['id']) as rt:
                    self._route_table_routes_action(
                        'add', routes, rt['route_table']['id'],
                        expected_code=exc.HTTPBadRequest.code)

    def test_no_destination_routes(self):
        self._test_add_invalid_routes(
            [{'nexthop': '********', 'type': 'ipv4'}])

    def test_no_nexthop_routes(self):
        self._test_add_invalid_routes(
            [{'destination': '***********/16', 'type': 'ipv4'}])

    def test_no_type_routes(self):
        self._test_add_invalid_routes(
            [{'nexthop': '********', 'destination': '***********/16'}])

    def test_not_supported_type(self):
        cfg.CONF.set_override('supported_routes_nexthop_type',
                              ['ecs', 'ipv4'])
        self._test_add_invalid_routes([{'nexthop': '********', 'type': 'eip',
                                        'destination': '***********/16'}])

    def test_none_destination(self):
        self._test_add_invalid_routes([{'destination': None, 'type': 'ecs',
                                        'nexthop': '********'}])

    def test_none_nexthop(self):
        self._test_add_invalid_routes([{'destination': '***********/16',
                                        'type': 'ecs', 'nexthop': None}])

    def test_add_exceed_routes(self):
        cfg.CONF.set_override('max_route_table_routes', 2)
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'},
                  {'destination': '********/8',
                   'nexthop': '********', 'type': 'ecs'},
                  {'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        data = {'route_table': {'routes': routes}}
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'])
                self.assertRaises(rt_exc.RouteTableRoutesExhausted,
                                  self.rt_plugin.add_route_table_routes,
                                  context=self.ctx, id=rt['route_table']['id'],
                                  route_table=data)

    def test_create_exceed_route_table(self):
        cfg.CONF.set_override('max_route_tables', 2)
        with self.router() as r:
            self._create_route_table(self.fmt, r['router']['id'])
            rt_num = self.rt_plugin.get_route_tables(
                self.ctx, filters={'router_id': [r['router']['id']]})
            self.assertEqual(2, len(rt_num))
            route_table = {'route_table': {'router_id': r['router']['id']}}
            self.assertRaises(rt_exc.RouteTablesExhausted,
                              self.rt_plugin.create_route_table,
                              context=self.ctx, route_table=route_table)

    def test_add_duplicate_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(
                    self.fmt, r['router']['id'], routes=routes)
                self.assertRaises(n_exc.BadRequest,
                                  self.rt_plugin.add_route_table_routes,
                                  context=self.ctx,
                                  id=rt['route_table']['id'],
                                  route_table={
                                      'route_table': {'routes': routes}})

    def test_unreachable_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '100.0.2.3', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                with self.route_table(r['router']['id']) as rt:
                    self.assertRaises(rt_exc.InvalidRoutes,
                                      self.rt_plugin.add_route_table_routes,
                                      context=self.ctx,
                                      id=rt['route_table']['id'],
                                      route_table={
                                          'route_table': {'routes': routes}})

    def test_nexthop_is_used_by_router(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                with self.port(subnet=s) as p:
                    self._router_interface_action(
                        'add', r['router']['id'], None, p['port']['id'])
                    port_ip = p['port']['fixed_ips'][0]['ip_address']
                    routes = [{'destination': '***********/16',
                               'nexthop': port_ip, 'type': 'ecs'}]
                    rt = self._create_route_table(self.fmt, r['router']['id'])
                    self._route_table_routes_action(
                        'add', routes, rt['route_table']['id'],
                        expected_code=exc.HTTPBadRequest.code)

    def test_routes_with_invalid_ip_address(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                with self.route_table(r['router']['id']) as rt:
                    rt_id = rt['route_table']['id']
                    routes = [{'destination': '512.207.0.0/16',
                               'nexthop': '********', 'type': 'ecs'}]
                    self._route_table_routes_action(
                        'add', routes, rt_id,
                        expected_code=exc.HTTPBadRequest.code)

                    routes = [{'destination': '***********/48',
                               'nexthop': '********', 'type': 'ecs'}]
                    self._route_table_routes_action(
                        'add', routes, rt_id,
                        expected_code=exc.HTTPBadRequest.code)

                    routes = [{'destination': 'invalid_ip_address',
                               'nexthop': '********'}]
                    self._route_table_routes_action(
                        'add', routes, rt_id,
                        expected_code=exc.HTTPBadRequest.code)

    def test_routes_with_invalid_nexthop(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                with self.route_table(r['router']['id']) as rt:
                    routes = [{'destination': '***********/16',
                               'nexthop': ' 300.10.10.4', 'type': 'ecs'}]
                    self._route_table_routes_action(
                        'add', routes, rt['route_table']['id'],
                        expected_code=exc.HTTPBadRequest.code)

    def test_remove_router_interface_in_use(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(
                    self.fmt, r['router']['id'], routes=routes)
                self.assertEqual(routes, rt['route_table']['routes'])
                self._router_interface_action(
                        'remove', r['router']['id'], s['subnet']['id'], None,
                        expected_code=exc.HTTPConflict.code)

    def test_remove_router_gateway_in_use(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._set_net_external(s['subnet']['network_id'])
                self._add_external_gateway_to_router(
                    r['router']['id'],
                    s['subnet']['network_id'])
                body = self._show('routers', r['router']['id'])
                net_id = body['router']['external_gateway_info']['network_id']
                self.assertEqual(net_id, s['subnet']['network_id'])

                with self.port(subnet=s) as p:
                    next_hop = p['port']['fixed_ips'][0]['ip_address']
                    routes = [{'destination': '***********/16',
                               'nexthop': next_hop, 'type': 'ecs'}]
                    self._create_route_table(
                        self.fmt, r['router']['id'], routes=routes)
                    self._remove_external_gateway_from_router(
                        r['router']['id'], s['subnet']['network_id'])
                    body = self._show('routers', r['router']['id'])
                    gw_info = body['router']['external_gateway_info']
                    self.assertIsNone(gw_info)

    def test_update_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                with self.route_table(r['router']['id'], routes=routes) as rt:
                    self.assertEqual(routes, rt['route_table']['routes'])
                    update_routes = [{'destination': '***********/16',
                                      'nexthop': '********', 'type': 'ipv4'}]
                    res = self._route_table_routes_action(
                        'update', update_routes, rt['route_table']['id'])
                    self.assertIn(update_routes[0], res['routes'])
                    self.assertNotIn({'destination': '***********/16',
                                      'nexthop': '********', 'type': 'ipv4'},
                                     res['routes'])

    def test_update_routes_with_unreachable_nexthop(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ipv4'},
                  {'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                with self.route_table(r['router']['id'], routes=routes) as rt:
                    self.assertEqual(routes, rt['route_table']['routes'])
                    update_routes = [{'destination': '***********/16',
                                      'nexthop': '*********', 'type': 'ipv4'}]
                    self._route_table_routes_action(
                        'update', update_routes, rt['route_table']['id'],
                        expected_code=exc.HTTPBadRequest.code)

    def test_update_default_route_table(self):
        with self.router() as r:
            default_id = self.rt_plugin._get_default_rt_id(
                self.ctx, r['router']['id'])
            self.assertIsNotNone(default_id)
            data = {'route_table': {'name': 'rt default'}}
            self._update('route_tables', default_id, data,
                         expected_code=exc.HTTPBadRequest.code)

    def test_delete_default_route_table(self):
        with self.router() as r:
            default_id = self.rt_plugin._get_default_rt_id(
                self.ctx, r['router']['id'])
            self.assertIsNotNone(default_id)
            self._delete('route_tables', default_id,
                         expected_code=exc.HTTPBadRequest.code)

    def test_delete_default_route_table_after_router_delete(self):
        with self.router() as r:
            default_id = self.rt_plugin._get_default_rt_id(
                self.ctx, r['router']['id'])
            self.assertIsNotNone(default_id)
            self._delete('routers', r['router']['id'])
            self.assertRaises(n_exc.NotFound,
                              self.l3_plugin.get_router,
                              context=self.ctx,
                              id=r['router']['id'])
            rt_id = self.rt_plugin._get_default_rt_id(
                self.ctx, r['router']['id'])
            self.assertIsNone(rt_id)

    def test_default_route_table_add_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                default_id = self.rt_plugin._get_default_rt_id(
                    self.ctx, r['router']['id'])
                res = self._route_table_routes_action(
                    'add', routes, default_id)
                self.assertEqual(routes, res['routes'])

    def test_default_route_table_update_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                default_id = self.rt_plugin._get_default_rt_id(
                    self.ctx, r['router']['id'])
                res = self._route_table_routes_action(
                    'add', routes, default_id)
                self.assertEqual(routes, res['routes'])
                update = [{'destination': '***********/16',
                           'nexthop': '10.0.1.100', 'type': 'ecs'}]
                res = self._route_table_routes_action(
                    'update', update, default_id)
                self.assertIn(update[0], res['routes'])

    def test_default_route_table_delete_routes(self):
        routes = [{'destination': '***********/16',
                   'nexthop': '********', 'type': 'ecs'}]
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                default_id = self.rt_plugin._get_default_rt_id(
                    self.ctx, r['router']['id'])
                res = self._route_table_routes_action(
                    'add', routes, default_id)
                self.assertEqual(routes, res['routes'])
                remove = [{'destination': '***********/16'}]
                res = self._route_table_routes_action(
                    'remove', remove, default_id)
                self.assertNotIn(
                    remove[0]['destination'],
                    [route['destination'] for route in res['routes']])

    def test_default_route_table_associate_subnets(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                default_id = self.rt_plugin._get_default_rt_id(
                    self.ctx, r['router']['id'])
                subnets = [s['subnet']['id']]
                self._action_subnets('associate', subnets, default_id,
                                     expected_code=exc.HTTPClientError.code,
                                     default=True)

    def test_default_route_table_disassociate_subnets(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                default_id = self.rt_plugin._get_default_rt_id(
                    self.ctx, r['router']['id'])
                subnets = [s['subnet']['id']]
                self._action_subnets('disassociate', subnets, default_id,
                                     expected_code=exc.HTTPClientError.code,
                                     default=True)

    def test_associate_subnets(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'])
                subnets = [s['subnet']['id']]
                self._action_subnets('associate', subnets,
                                     rt['route_table']['id'])
                binds = self.rt_plugin.get_associated_subnets(
                    self.ctx, {'routetable_id': [rt['route_table']['id']]})
                self.assertIn(subnets[0], [b['subnet_id'] for b in binds])

    def test_associate_subnets_not_connect_to_router(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                rt = self._create_route_table(self.fmt, r['router']['id'])
                subnets = [s['subnet']['id']]
                self._action_subnets(
                    'associate', subnets, rt['route_table']['id'],
                    expected_code=exc.HTTPBadRequest.code)

    def test_disassociate_subnets(self):
        with self.router() as r:
            with self.subnet(cidr='********/24') as s:
                self._router_interface_action(
                    'add', r['router']['id'], s['subnet']['id'], None)
                rt = self._create_route_table(self.fmt, r['router']['id'])
                subnets = [s['subnet']['id']]
                self._action_subnets(
                    'associate', subnets, rt['route_table']['id'])
                binds = self.rt_plugin.get_associated_subnets(
                    self.ctx, {'routetable_id': [rt['route_table']['id']]})
                self.assertIn(subnets[0], [b['subnet_id'] for b in binds])

                self._action_subnets(
                    'disassociate', subnets, rt['route_table']['id'])
                binds = self.rt_plugin.get_associated_subnets(
                    self.ctx, {'routetable_id': [rt['route_table']['id']]})
                self.assertNotIn(subnets[0], [b['subnet_id'] for b in binds])
